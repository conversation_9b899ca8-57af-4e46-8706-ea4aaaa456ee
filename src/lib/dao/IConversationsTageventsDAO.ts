import * as pg from "pg";
import { ConversationtagDto, ConversationtageventDto, ConversationtagPkDto } from "whatsapp-srv-dtos";

export type ConversationstageventsFilter = {
  /** Identificador del avatar */
  conversation_avatar_id?: number
  /** identificador del contacto */
  conversation_contact_id?: number
  /** Identificador del dominio */
  tag_id?: number
  /** 
   * El conversationtag debe pertenecer al dominio indicado.
   *  Por definición, El tag, el avatar y el contacto de un conversationtag pertenecen a ese mismo dominio.  Por eso 
   *  no hay un campo "doamin" en la entidad conversation (Internamente, buscamos conversaciones cuyo avatar sea del dominio)
   */
  domain_id?: number,
  /** Detallar datos adicionales del tag (además de su id) */
  detail_tag?: boolean,
  /** Detallar datos adicionales del contaco (además de su id) */
  detail_conversation_contact?: boolean,
  /** Detallar datos adicionales del avatar (además de su id) */
  detail_conversation_avatar?:boolean,
}

export interface IConversationstageventsDAO {
  list(filter?: ConversationstageventsFilter): (dbCli?: pg.PoolClient) => Promise<ConversationtageventDto[]>;
  count(filter?: ConversationstageventsFilter): (dbCli?: pg.PoolClient) => Promise<number>;
}