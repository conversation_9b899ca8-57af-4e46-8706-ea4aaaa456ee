import { IContainer } from "container/IContainer";
import { sleep } from "lib/utils/SystemUtils";
import { EventEmitter } from "stream";
import { jsonMember, jsonObject } from "typedjson";
import WAWebJS, { Client, LocalAuth, MessageContent } from 'whatsapp-web.js';
import { IWhatsappClients } from "../IWhatsappClients";

/**
 * Gestiona todas las instancias de whatsappweb. 
 * @param deps 
 */
export function WhatsappClients({ waclientsOptions: wawebSrvOptions, smtp, logger }: IContainer): IWhatsappClients {

  let notReadyClients: Map<string, Client> = new Map();
  // Clientes que están esperando binding.  Por cada cliente indicamos el QR solicitado.
  let awaitingBindingQR: Map<string, string> = new Map();
  // Clientes que ya han sido bindeados (autenticados), pero aún no hemos validado que el binding se corresponda con el esperado (mismo número de teléfono)
  let awaitingBindingValidation: Set<string> = new Set();
  // Contador de envíos consecutivos erróneos... si persiste

  let readyClients: Map<string, Client> = new Map();
  let emmiter = new MessagesEmiter();

  return {
    bootstrapClient,
    shutdownClient,
    getClientStatus,
    getClientSnapshotAsJpeg,
    listContacts,
    getContact,
    sendMessage,
    listMessages,
    emmiter,
    dispose,
  };


  async function shutdownClient(clientNumber: string) {
    for (const clients of [notReadyClients, readyClients])
      if (clients.has(clientNumber)) {
        const client = clients.get(clientNumber);
        clients.delete(clientNumber);
        await client?.destroy();
      }
    //
    awaitingBindingQR.delete(clientNumber);
  }
  async function dispose() {
    const toDestroy = [...notReadyClients.values(), ...readyClients.values()];
    notReadyClients.clear();
    readyClients.clear();
    for (const client of toDestroy)
      try {
        await client.destroy()
      } catch (e) {
        logger.error(`Problems destroying client ${client.info.wid}`)
      };
    if (emmiter.eventNames().filter(name => emmiter.listenerCount(name) !== 0).length !== 0) {
      logger.warn("Quedan event listeners que no han sido eliminados");
    }
  }

  async function getClientSnapshotAsJpeg(clientNumber: string) {
    let client = await getClient(clientNumber);
    if (client?.pupPage) {
      let buffer = await client.pupPage?.screenshot({
        fullPage: true,
        encoding: "binary",
        type: "jpeg"
      });
      return buffer as Buffer
    } else {
      return void 0;
    }
  }

  function getClientStatus(clientNumber: string): WhatsappClients.clientStatus {
    if (readyClients.has(clientNumber))
      return WhatsappClients.clientStatus.ready;
    else if (notReadyClients.has(clientNumber))
      return WhatsappClients.clientStatus.notReady;
    else
      return WhatsappClients.clientStatus.unknown;
  }
  /**
   * Todos los contactos con los que el usuario tiene activo algún chat.
   * @param clientNumber 
   * @returns 
   */
  async function listContacts(clientNumber: string): Promise<WAWebJS.Contact[]> {
    const client = await getReadyClient(clientNumber);
    const contacts = await client.getContacts();
    return contacts;
  }

  async function getContact(clientNumber: string, contactNumber: string): Promise<WAWebJS.Contact | undefined> {
    const client = await getReadyClient(clientNumber);
    const contactId = await client.getNumberId(contactNumber).catch(e => {
      if (e instanceof Error && e.message.includes("wid error: invalid wid")) {
        // Algunos números de teléfono fallan sin razón "aparente" (error de formanto?).  Nos 
        // protegemos devolviendo que no hemos conseguido el numberid del contacto.
        logger.warn(`getContact: intervenido wid error: invalid wid al evocar getNumberId( "${contactNumber}" )`);
        return null;
      } else
        throw e;
    });
    if (contactId)
      return client.getContactById(contactId._serialized);
    else
      throw new IWhatsappClients.UnknownPhonenumberError(`${clientNumber}. Unknown number ${contactNumber}`);

  }



  /**
   * 
   * @param clientNumber Usuario que envía el mensaje.  Debe haberse inicializado con el método bootstrapClient
   * @param toNumber 
   * @param message 
   * @returns 
   */
  async function sendMessage(clientNumber: string, toNumber: string, message: MessageContent) {
    const chat = await getChat(clientNumber, toNumber);
    try {
      return await chat.sendMessage(message);
    } catch (e) {
      if (e instanceof Error) {
        if (e.message.includes("Cannot read properties of undefined (reading 'features')")) {
          logger.info("El cliente está dando problemas al enviar mensajes... cerramos el pupetter y lo eliminamos de la lista de pendientes");
          try { await destroyClientIfReady(clientNumber) } catch (e) { }
        }
      }
      throw e;
    }
  }

  /**
   * Destruye un cliente, siempre y cuando esté en la lista de Ready, 
   * @param clientNumber 
   */
  async function destroyClientIfReady(clientNumber: string) {

    const cli = readyClients.get(clientNumber);
    if (cli) {
      await cli.logout();
      await cli.destroy();
      readyClients.delete(clientNumber);
    }

  }
  async function listMessages(clientNumber: string, withNumber: string) {
    const chat = await getChat(clientNumber, withNumber);
    if (!chat.archived) {
      const messages = await chat.fetchMessages({});
      return messages;
    } else {
      return [];
    }
  }



  /**
   * Inicia la aplicación asociada al número de teléfono.
   * Si ésta requirere verificar QR, se notifica (eento "bind")
   * Si no se consigue activar, se devuelve un error
   * Si se activa un cliente correspondiente a un teléfono distinto, se cerrará la sesión y se volverá a empezar.
   * @param clientNumber 
   * @param notificationEmail 
   * @returns 
   */
  async function bootstrapClient(clientNumber: string): Promise<void> {
    if (getClientStatus(clientNumber) === WhatsappClients.clientStatus.unknown)
      await createClient(clientNumber, false);
    else
      throw new Error("Client alredy bootstraped");

  }

  async function createClient(clientNumber: string, cleanupAll: boolean): Promise<void> {
    return new Promise(async (resolve, reject) => {

      // Hay varias razones concurrentes para resolver/rechazar la promesa... nos aseguramos de que solo una de ellas se lanza.
      const { resolveOnce, rejectOnce } = ((solved = false) => (
        {
          resolveOnce: () => {
            if (!solved) { solved = true; resolve(); }
          },
          rejectOnce: (reason?: any) => {
            if (!solved) { solved = true; reject(reason); }
          }
        }
      ))();


      try {
        const authStrategy = new LocalAuth({ dataPath: wawebSrvOptions.localAuth.dataPath, clientId: clientNumber });

        // Usado para diferenciar si la autenticación es precedida de una solicitud de QR previa


        const client = new Client({
          puppeteer: { headless: true, args: ['--no-sandbox'] },
          authTimeoutMs: wawebSrvOptions.authTimeoutMs,
          authStrategy: authStrategy

        }).
          on('qr', async qr => {
            try {
              logger.debug(`${clientNumber}: client.on('qr'), qr: ${qr}`);
              awaitingBindingQR.set(clientNumber, qr);
              emmiter.emitBind({ clientNumber, qr });
              //await sendQr(clientNumber, qr);
            } catch (e) {
              logger.error(`${clientNumber}: Problemas procesando qr`, e);
            }
          }).
          on('authenticated', async (session) => {
            logger.info(`${clientNumber}: client.on('authenticated'). session: ${session}`);
          }).
          on('auth_failure', async (message) => {
            logger.info(`${clientNumber}: client.on('auth_failure'): ${message}`);
            await cleanupNotReadyClient();
            rejectOnce("Auth failure");
          }).
          on('change_state', async (state) => {
            logger.debug(`${clientNumber}: client.on('change_state')  state: ${state}`);
          }).
          on('ready', async () => {
            logger.debug(`${clientNumber} client.on('ready')`);
            notReadyClients.delete(clientNumber);
            // 1.- Nos aseguramos de que el cliente se corresponde con el número.
            const bindedNumber = client.info.wid.user;
            if (bindedNumber !== clientNumber) {
              logger.info(`${clientNumber}: Se ha vinculado usuario incorrecto ${bindedNumber}. Se procede al cierre de sesión`);
              // Decimos a whatsapp que haga un logout
              try { await client.logout(); } catch (e) { logger.error(`${clientNumber} client.logout()`, e); }
              // Esperamos unos segundos antes de destruir el cliente para dar tiempo a que client.logout() haga su magia :-p 
              await new Promise(resolve => setTimeout(resolve, 5000));
              // Cerramos el cliente (puppeter)
              try { await client.destroy(); } catch (e) { logger.error(`${clientNumber} client.destroy()`, e); }

              if (awaitingBindingQR.has(clientNumber)) awaitingBindingQR.delete(clientNumber);
              // Siempre informamos del BadBinding, aunque no hayamos pasado por la petición de QR (el cliente podría haberse "cerrado" tras la autenticación y al activarlo no se solicita QR)
              emmiter.emitBadBinding({ clientNumber, bindedNumber });
              rejectOnce("Invalid binding.  Client has been dropped");
            } else {
              readyClients.set(clientNumber, client);
              if (awaitingBindingQR.has(clientNumber)) { awaitingBindingQR.delete(clientNumber); emmiter.emitBinded({ clientNumber }); }
              resolveOnce();
            }
          }).
          on('message', message => {
            logger.debug(`${clientNumber}: on('message'):`, message, message.body);
            emmiter.emitMessageReceived({ clientNumber: clientNumber, message })
          }).
          on('disconnected', reason => {
            logger.debug(`${clientNumber}: on('disconnected'): ${reason}`);
            notReadyClients.delete(clientNumber);
            readyClients.delete(clientNumber);
          });
        logger.info(`Initializing client ${clientNumber}`);
        notReadyClients.set(clientNumber, client);
        try { await client.initialize(); } catch (e) {
          // Este error sucede tras el timeout intentando hacer login
          await cleanupNotReadyClient();
          throw e;
        }


        async function cleanupNotReadyClient() {
          if (notReadyClients.has(clientNumber)) {
            try {
              // Lo quitamos de la carpeta de not ready
              notReadyClients.delete(clientNumber);

              awaitingBindingQR.delete(clientNumber);
              // Cerramos el cliente (se cierra navegador puppeteer)

              await client.destroy();
              // Si se ha conseguido establecer, borra la carpeta local donde reside la sesión (para empezar una navegación limpia la próxima vez)
              await authStrategy.logout();
            } catch (e) { logger.error(`${clientNumber}: client.destroy()`, e); }
          }
        }

      } catch (e) {
        logger.error("Unexpected error when bootstraping client", e);
        reject(e);
      }


    });


  }

  /**
   * Obtiene el chat con un contacto cuyo número de teléfono sea el indicado
   * @param clientNumber 
   * @param contactNumber 
   * @returns 
   */
  async function getChat(clientNumber: string, contactNumber: string): Promise<WAWebJS.Chat> {
    const client = await getReadyClient(clientNumber);
    const toId = await client.getNumberId(contactNumber);
    if (!toId) throw new IWhatsappClients.UnknownPhonenumberError(`${clientNumber}. Unknown number ${contactNumber}`);
    const toContact = await client.getContactById(toId._serialized);
    if (!toContact) throw new IWhatsappClients.UnknownContactError(`${clientNumber}. Unknown contact ${contactNumber}`);
    const chat = await toContact.getChat();
    if (!chat) throw new IWhatsappClients.NoContactChatError(`${clientNumber}. Can't obtain chat ${contactNumber}`);
    return chat;
  }

  /**
   * Espera a que el cliente esté listo antes de devolverlo.
   * Si no hay un cliente asociado al número, o pasa demasiado tiempo esperando, fallará
   * @param clientNumber 
   * @param tries 
   * @returns 
   */
  async function getReadyClient(clientNumber: string, tries: number = 0) {
    let status = getClientStatus(clientNumber);
    tries--;
    while (status === WhatsappClients.clientStatus.notReady && tries > 0) {
      await sleep(500);
      status = getClientStatus(clientNumber);
      tries--;
    }

    const client = readyClients.get(clientNumber);
    if (!client) {
      if (status === WhatsappClients.clientStatus.unknown)
        throw new IWhatsappClients.UnknownClientError(`There is not client for ${clientNumber}`);
      else
        throw new IWhatsappClients.UnitializedClientError(`There is not a initialized client for ${clientNumber}`);
    } else {
      return client;
    }
  }

  /**
   * Deveuelve el cliente de whatsapp asociado al número indicado sea cuan sea su estado (Ready o NotReady)
   * Si no se encuentra un cliente para el número, se retorna undefined
   * @param clientNumber 
   * @returns EL Cliente de Whatsapp si está cargado (sea cual sea su estado) o undefined en caso contrario
   */
  function getClient(clientNumber:string): WAWebJS.Client | undefined{
    const client = readyClients.get(clientNumber) ?? notReadyClients.get(clientNumber);
    return client
  }




}


export namespace WhatsappClients {
  /** Estado de un cliente */
  export enum clientStatus {
    // En preparación.   
    notReady = "not_ready",
    // Está listo
    ready = "ready",
    // No se ha conseguido establecer el cliente.  Puedes reintentar más tarde (se aconseja, de hecho)
    unknown = "unknown"
  }

  export declare interface IMessagesEmiter {
    on(event: "message_received", listener: (data: { clientNumber: string, message: WAWebJS.Message }) => void): this
    on(event: "bind", listener: (data: { clientNumber: string, qr: string }) => void): this
    on(event: "bad_binding", listener: (data: { clientNumber: string, bindedNumber: string }) => void): this
    on(event: "binded", listener: (data: { clientNumber: string }) => void): this
    off(event: string, listener: (...args: any[]) => void): this
  }


  @jsonObject()
  export class LocalAuthOptions {
    @jsonMember(String, { isRequired: true })
    dataPath!: string
  }
  @jsonObject()
  export class Options {
    @jsonMember(Number, { isRequired: true })
    authTimeoutMs!: number
    @jsonMember(LocalAuthOptions, { isRequired: true })
    localAuth!: LocalAuthOptions
  }

}
enum eventNames {
  message_received = "message_received",
  bind = "bind",
  bad_binding = "bad_binding",
  binded = "binded"
}

class MessagesEmiter extends EventEmitter implements WhatsappClients.IMessagesEmiter {

  constructor() { super(); }
  emitMessageReceived(data: { clientNumber: string, message: WAWebJS.Message }): boolean {
    return super.emit(eventNames.message_received, data);
  }
  emitBind(data: { clientNumber: string, qr: string }): boolean {
    return super.emit(eventNames.bind, data)
  }
  emitBadBinding(data: { clientNumber: string, bindedNumber: string }): boolean {
    return super.emit(eventNames.bad_binding, data)
  }
  emitBinded(data: { clientNumber: string }): boolean {
    return super.emit(eventNames.binded, data)
  }
  off(eventName: string, listener: (...args: any[]) => void) {
    return super.removeListener(eventName, listener);
  }


}
