import { RestError, throwUnexpectedError } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { Request } from "express";
import { IWAClientsBusiness } from "lib/bussiness/IWAClientsBusiness";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { jsonMember, jsonObject, jsonArrayMember } from "typedjson";
import { ConversationDto, MessageDirection, WAAccountDto } from "whatsapp-srv-dtos";
import { IWAMessagesCtrl } from "../IWAMessagesCtrl";

export function WAMessagesCtrl(
  {
    waaccountsDAO,
    wamessagesDAO,
    wacontactsDAO,
    conversationsDAO,
    waclientsBusiness,
    restUtils: { asRest, asValidRequest, withParsedRequest },
    controllerUtils: { withAuthorizedUser }
  }: IContainer
): IWAMessagesCtrl {

  return {
    postMessage: asRest({ nullAs404: true }, asValidRequest("/wamessages/post_message", postMessage)),
    postConversationMessage: asRest({ nullAs404: true }, postConversationMessage),
    getMessages: asRest({ nullAs404: true }, getMessages)
  }

  function postConversationMessage(req: Request) {
    return withParsedRequest(PostConversationMessageRequest, ({ params: { avatar_id, contact_id }, body: { body } }) =>
      withAuthorizedUser("message_post", async (user) => {
        // La conversación debe existir en el dominio del usuario
        const [conversation] = await conversationsDAO.list({
          domain_id: user.domain.id,
          avatar_id,
          contact_id,
          include_avatar: true,
          include_contact: true,
        })();
        if (!conversation)
          return null; // Not found

        const waaccount_id = await bestConversationWAAccount(conversation);
        if (!waaccount_id)
          throw RestError.internal({ message: `No active Whatsapp client found for the conversation.` });

        return sendMessage(
          waaccount_id,
          conversation?.contact?.phonenumber ?? throwUnexpectedError("Missing conversation contact's phonenumber"),
          body
        );



      })(req)
    )(req);
  }


  /**
   * Envía el mensaje a través del waclientsBusiness (registrandolo en la BBDD si tiene éxito)
   * @param from teléfono (===id) de la waaccount desde la que se envía
   * @param to teléfono (===id si existe) del wacontact al que va dirigido el mensaje
   * @param body texto del mensaje (cuerpo del mensaje)
   * @returns WAMessageDTO enviado (y registrado en la BBDD)
   */
  async function sendMessage(from: string, to: string, body: string) {
    const result = await waclientsBusiness.send(from, to, body);
    switch (result.code) {
      case IWAClientsBusiness.sendResultCode.ok: return result.sentMessage;
      case IWAClientsBusiness.sendErrorCode.err_unknown_contact:
        throw RestError.badRequest({ message: "Unknown destination contact", code: result.code });
      case IWAClientsBusiness.sendErrorCode.err_no_chat:
        throw RestError.badRequest({ message: "Cant find destination contact chat", code: result.code });
      default:
        throw RestError.internal({ message: `Whatsapp client not ready.  Try it latter. (code:${result.code})` })
    }
  }

  /**
   * Investiga qué waaccount elegir para enviar un mensaje en nombre del avatar al contacto indicado.
   * Dado que hay N waaccounts asociadas a un avatar usamos el siguiente algoritmo
   * -> Si el último mensaje de la conversación se envió desde una waaccount activa, ya está
   * -> Si hay una waaccount activa con un wacontact asoiciado con el teléfono del contacto, ya está
   * -> Si hay una waaccount activa, ya está
   * -> ERROR->No hay ningún waaccount activo asociado al avatar
   * @param conversation 
   * @returns  El número de teléfono del waaccount y el número de teléfono destino a emplear en el envío del mensaje
   */
  async function bestConversationWAAccount(conversation: ConversationDto) {
    const { avatar, contact } = conversation;
    const avatar_id = avatar?.id ?? throwUnexpectedError("Missing conversation's avatar indentifier");
    const contact_id = contact?.id ?? throwUnexpectedError("Missing conversation's contact indentifier");
    const contact_phonenumber = contact?.phonenumber ?? throwUnexpectedError("Missing conversation's contact phonenumber");

    // 1.- Buscamos el mensaje más nuevo intercambiado en la conversación... eso nos indicará la cuenta de whatsapp empleada (y el contacto whatsapp)
    const [wamessage] = await wamessagesDAO.list({
      // solo el último mensaje
      onlylast: true,
      // de una cuenta de whatsapp que esté habilitada
      account_enabled: true,
      // para la conversación indicada
      conversation_avatar_id: avatar_id, conversation_contact_id: contact_id
    })();
    if (wamessage)
      return wamessage.account?.id ?? throwUnexpectedError("Missing waaccount phone number");

    // 2.- Buscamos por teléfono un unwacontact de una cuenta activa asociada al avatar
    const [wacontact] = await wacontactsDAO.list({
      account_enabled: true,
      account_avatar_id: avatar_id,
      phonenumber: contact_phonenumber
    })();
    if (wacontact)
      return wacontact.account?.id ?? throwUnexpectedError("Missing waaccount phone number");

    // 3.- Buscamos cualquier cuenta activa asociada al avatar y elejimos una en función (hash) del número de teléfono del contacto
    const waaccounts = await waaccountsDAO.list({ enabled: true, avatar_id: avatar_id })();
    const waaccount = selectOne(waaccounts, contact_phonenumber);
    if (waaccount)
      return waaccount.id; // Ide de un waacount es su número de teléfono normalizado.
    else
      return undefined;


    /**
     * Elige una de las cuentas de whatsapp indicadas aplicando un hash al número de teléfono al que hay que enviar un mensaje.
     * Si el conjunto de cuentas cambia, el resultado de la función cambiará.
     * @param accounts Cuentas entre las que elegiremos una.  Si es una array vacía, la elección será <undefined>
     * @param phonenumber Clave de selección.  Solo tenemos en cuenta sus dígitos numéricos
     * @returns Una de las cuentas.  <undefined> si no se ha indicado ninguna cuenta
     */
    function selectOne(accounts: WAAccountDto[], phonenumber: string): WAAccountDto | undefined {
      if (accounts.length === 0)
        return void 0;
      const lastDigits = "0" + phonenumber.
        // Procesamos caracter a caracter
        split("").
        // Sólo queremos los dígitos numéricos
        filter(c => c >= '0' && c <= '9').
        // y de ellos solo los últimos 4 (son los que "más cambian")
        splice(-4).
        // Generamos la cadena numérica
        join("");

      const sorted = [...accounts].sort((a, b) => a.id! > b.id! ? 1 : a.id! < b.id! ? -1 : 0);
      return sorted[parseInt(lastDigits) % accounts.length];

    }


  }



  function postMessage(req: Request) {

    return withParsedRequest(PostMessageRequest, ({ body: message }) =>
      withAuthorizedUser("message_post", async () => {
        const account = await waaccountsDAO.read({ id: message.account.id })();
        if (!account)
          return void 0
        else
          sendMessage(account.id!, message.to.id, message.body);
      })(req)
    )(req);

  }

  function getMessages(req: Request) {

    return withParsedRequest(GetMessagesRequest, ({ query }) =>
      withAuthorizedUser("message_get", () =>
        wamessagesDAO.list(query)()
      )(req)
    )(req);

  }
}

//#region estructuras de datos con las que parseamos params/query/body



@jsonObject
class IdedEntity {
  @jsonMember(String, { isRequired: true })
  id!: string
}
@jsonObject
class PostMessageBody {
  @jsonMember(IdedEntity, { isRequired: true })
  account!: IdedEntity
  @jsonMember(IdedEntity, { isRequired: true })
  to!: IdedEntity
  @jsonMember(String, { isRequired: true })
  body!: string
  @jsonMember(String, { isRequired: true })
  type!: string
}
@jsonObject
class PostMessageRequest {
  @jsonMember(PostMessageBody, { isRequired: true })
  body!: PostMessageBody
}

// #retion Post ConversationMessage
@jsonObject
class PostConversationMessageParams {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  avatar_id!: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  contact_id!: number
}
@jsonObject
class PostConversationMessageBody {
  @jsonMember(String, { isRequired: true })
  body!: string
  @jsonMember(String, { isRequired: true })
  type!: string
}
@jsonObject
class PostConversationMessageRequest {
  @jsonMember(PostConversationMessageParams, { isRequired: true })
  params!: PostConversationMessageParams
  @jsonMember(PostConversationMessageBody, { isRequired: true })
  body!: PostConversationMessageBody
}
// #endregion

@jsonObject
class GetMessagesQuery {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  conversation_avatar_id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  conversation_contact_id?: number
  @jsonMember(String)
  conversation_contact_phonenumber?: string
  @jsonArrayMember(Number, { deserializer: TypedJsonUtils.strAsArrayIntDeserializer() })
  conversation_tags_id_someof?: number[]
  @jsonArrayMember(Number, { deserializer: TypedJsonUtils.strAsArrayIntDeserializer() })
  conversation_tags_id_noneof?: number[]
  @jsonMember(String)
  account_id?: string
  @jsonMember(String)
  id?: string
  //@jsonArrayMember(String)
  //ids?: string[]
  @jsonMember(String)
  from_id?: string
  @jsonMember(String)
  to_id?: string
  @jsonMember(String)
  /** EL contacto asociado a mi cuenta */
  me_id?: string
  /** El otro que no soy yo (sea from o to) */
  @jsonMember(String)
  other_id?: string
  /** stamp mayor o igual que */
  @jsonMember(Date)
  stamp_gte?: Date
  @jsonMember(Date)
  uniquestamp_gte?: Date
  /** stamp mayor o igual que */
  @jsonMember(Date)
  stamp_gt?: Date
  @jsonMember(Date)
  uniquestamp_gt?: Date
  /** stamp menor estricto de */
  @jsonMember(Date)
  stamp_lt?: Date
  @jsonMember(Date)
  uniquestamp_lt?: Date
  /** stamp menor o igual que */
  @jsonMember(Date)
  stamp_lte?: Date
  @jsonMember(Date)
  uniquestamp_lte?: Date
  @jsonMember(String, { deserializer: TypedJsonUtils.oneOfDeserializer(["in", "out"]) })
  direction?: MessageDirection
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer() })
  include_other?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer() })
  include_me?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer() })
  include_account?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer() })
  include_original?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer() })
  include_conversation?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer() })
  onlylast?: boolean

  @jsonMember(String, { deserializer: TypedJsonUtils.oneOfDeserializer(["stamp_asc", "stamp_desc", "id_asc", "id_desc"]) })
  orderby?: "stamp_asc" | "stamp_desc" | "id_asc" | "id_desc"
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  page_offset?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  page_limit?: number
}
@jsonObject
class GetMessagesRequest {
  @jsonMember(GetMessagesQuery, { isRequired: true })
  query!: GetMessagesQuery
}

///#region