#!/usr/bin/env rdmd
module admin.test;


/++
 + <PERSON>ript implementado en lenguaje d
 * Debes tener instalado rdmd en tu sistema (acompaña a dmd)
 + $ sudo apt install dmd
 + o bien
 + $ sudo snap install dmd --classic
 +
 +/
/*import std.stdio : writeln;
import std.format : format;
import std.file : exists;*/
import std;
import admin.lib.tools : cleanupDir, copyDir, exec, generateZip;

void main()
{
  import admin.vars : c_installer_dir, c_public_dir, c_agentorweb_dir;

  try
  {
    
    
    "test".generateZip("test.7z");

  }
  catch (Exception e)
  {
    writeln("Installer has not been generated");
    writeln(e);
  }
  finally
  {
    writeln("");
  }
}


