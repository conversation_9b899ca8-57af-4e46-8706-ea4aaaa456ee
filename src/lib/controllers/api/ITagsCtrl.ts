import { NextFunction, Request, Response } from "express";

export interface ITagsCtrl {
  //createAccount(req: Request, res: Response, next: NextFunction): void
  getTag(req: Request, res: Response, next: NextFunction): void
  getTags(req: Request, res: Response, next: NextFunction): void
  putTag(req: Request, res: Response, next: NextFunction): void
  postTag(req: Request, res: Response, next: NextFunction): void
  deleteTag(req: Request, res: Response, next: NextFunction): void
}