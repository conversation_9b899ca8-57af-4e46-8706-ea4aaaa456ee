{"type": "object", "properties": {"params": {"type": "object", "properties": {}}, "query": {"type": "object", "properties": {"conversation_avatar_id": {"$ref": "/common/notempty_integer_string"}, "conversation_contact_id": {"$ref": "/common/notempty_integer_string"}, "account_id": {"$ref": "/common/notempty_integer_string"}, "id": {"$ref": "/common/notempty_integer_string"}, "ids": {"type": "array", "items": {"$ref": "/common/notempty_integer_string"}}, "from_id": {"$ref": "/common/notempty_integer_string"}, "to_id": {"$ref": "/common/notempty_integer_string"}, "me_id": {"$ref": "/common/notempty_integer_string"}, "other_id": {"$ref": "/common/notempty_integer_string"}, "direction": {"enum": ["in", "out"]}, "onlylast": {"$ref": "/common/notempty_boolean_string"}, "stamp_gte": {"$ref": "/common/notempty_datetime_string"}, "stamp_lt": {"$ref": "/common/notempty_datetime_string"}, "stamp_lte": {"$ref": "/common/notempty_datetime_string"}, "uniquestamp_gt": {"$ref": "/common/notempty_datetime_string"}, "uniquestamp_gte": {"$ref": "/common/notempty_datetime_string"}, "uniquestamp_lt": {"$ref": "/common/notempty_datetime_string"}, "uniquestamp_lte": {"$ref": "/common/notempty_datetime_string"}, "include_other": {"$ref": "/common/notempty_boolean_string"}, "include_me": {"$ref": "/common/notempty_boolean_string"}, "include_original": {"$ref": "/common/notempty_boolean_string"}, "orderby": {"enum": ["stamp_asc", "stamp_desc", "id_asc", "id_desc"]}, "page_offset": {"$ref": "/common/notempty_integer_string"}, "page_limit": {"$ref": "/common/notempty_integer_string"}}, "required": [], "additionalProperties": false}}, "required": ["query"]}