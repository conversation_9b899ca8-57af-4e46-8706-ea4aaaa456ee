import { NextFunction, Request, Response } from "express";

export interface IConversationtagsCtrl {
  /** 
   *  Leer 1 Conversationtag 
   *  
   *  GET /conversationtags/:avatar_id/:contact_id/:tag_id
   * 
   *  200 ConversationtagDto
   */
  getConversationtag(req: Request, res: Response, next: NextFunction): void
  /** 
   *  Leer N Conversationtags según una condición (query)
   *  
   *  GET /conversationstags?...
   *
   *  200 ConversationtagDto[]
   */
  getConversationtags(req: Request, res: Response, next: NextFunction): void
  /** 
   *  Añadir 1 Conversationtag  
   *  POST /conversationstags
   *  body TagPkDto
   *  
   *  200 ConversationtagDto  El conversationtag recién creado
   */
  postConversationtag(req: Request, res: Response, next: NextFunction): void
  /** 
   *  Borrar 1 Conversationtag
   *  DELETE /conversationtags/:avatar_id/:contact_id/:tag_id
   *  Response
   *    200 TagPkDto   <- Clave del  Tag que ha sido eliminado de la conversación
   */
  deleteConversationtag(req: Request, res: Response, next: NextFunction): void
}