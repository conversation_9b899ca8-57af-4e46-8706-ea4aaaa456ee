import { DBSchema } from "agentor-lib";

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const usersFields = {
  id: "id",
  name: "name",
  domain_id: "domain_id"
}

export const usersTable = new DBSchema.TableSchema("users", [
  new DBSchema.TableFieldSchema(usersFields.id, { r, pk, nn }),
  new DBSchema.TableFieldSchema(usersFields.name, { c, r, u }),
  new DBSchema.TableFieldSchema(usersFields.domain_id, { c, r, nn }),
], [
  new DBSchema.TableFKSchema("domain")
]);
