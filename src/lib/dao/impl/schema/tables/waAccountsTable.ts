
import { DBSchema } from "agentor-lib"

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum waAccountsFields {
  id = "id",
  notificationemail = "notificationemail",
  enabled = "enabled",
  avatar_id = "avatar_id",
}

const fldN = waAccountsFields;

export const waAccountsTable = new DBSchema.TableSchema("waaccounts", [
  new DBSchema.TableFieldSchema(fldN.id, { r, pk, nn }),
  new DBSchema.TableFieldSchema(fldN.notificationemail, { c, r, u, nn }),
  new DBSchema.TableFieldSchema(fldN.enabled, { c, r, u, nn }),
  new DBSchema.TableFieldSchema(fldN.avatar_id, { c, r, nn }),
], [
  new DBSchema.TableFKSchema("avatar")
]);