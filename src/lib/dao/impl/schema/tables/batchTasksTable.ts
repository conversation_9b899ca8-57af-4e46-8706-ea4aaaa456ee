import { DBSchema } from "agentor-lib";

export enum batchTasksFields {
  id = "id",
  scheduling_start = "scheduling_start",
  scheduling_deadline = "scheduling_deadline",
  finished_at = "finished_at",
  created_at = "created_at",
}

const fldN = batchTasksFields;
const [c, r, u, pk, nn] = [true, true, true, true, true];

export const batchTasksTable: DBSchema.TableSchema = new DBSchema.TableSchema(
  "bat_tasks",
  [
    new DBSchema.TableFieldSchema(fldN.id, { r, pk, nn }),
    // Field associated to foreign key "agent" (this is a convention): <ForeigkKeyName>_<ReferencedTabkePKField>
    new DBSchema.TableFieldSchema(fldN.scheduling_start, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.scheduling_deadline, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.finished_at, { c, r, u }),
    new DBSchema.TableFieldSchema(fldN.created_at, { c, r, nn }),
  ],
  []
);