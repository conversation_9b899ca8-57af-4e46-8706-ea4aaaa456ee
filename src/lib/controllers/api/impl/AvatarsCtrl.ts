import { throwUndefinedError } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { Request } from "express";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { PoolClient } from "pg";
import { jsonMember, jsonObject } from "typedjson";
import { AvatarPkDto } from "whatsapp-srv-dtos";
import { IAvatarsCtrl } from "../IAvatarsCtrl";


export function AvatarsCtrl(
  {
    db: { withTransaction },
    avatarsDAO,
    restUtils: { asRest, withParsedRequest },
    controllerUtils: { withAuthorizedUser }
  }: IContainer
): IAvatarsCtrl {

  return {
    getAvatars: asRest({ nullAs404: true }, getAvatars),
    getAvatar: asRest({ nullAs404: true }, getAvatar),
    postAvatar: asRest({ nullAs404: true }, postAvatar),
    putAvatar: asRest({ nullAs404: true }, putAvatar),
    deleteAvatar: asRest({ nullAs404: true }, deleteAvatar),
  };


  function getAvatar(req: Request) {
    return withParsedRequest(GetAvatarRequest, ({ params }) =>
      withAuthorizedUser("avatar_get",
        async (user) => {
          const [avatar] = await avatarsDAO.list({ id: params.avatar_id, domain_id: user.domain.id })()
          return avatar;
        }
      )(req)
    )(req);
  }

  function getAvatars(req: Request) {
    return withParsedRequest(GetAvatarsRequest, ({ query }) =>
      withAuthorizedUser("avatars_get",
        async (user) => avatarsDAO.list({ ...query, domain_id: user.domain.id })()
      )(req)
    )(req);
  }

  function postAvatar(req: Request) {
    return withAuthorizedUser("avatars_post",
      async (usr) =>
        withTransaction(async (trx) => {
          const pk = await avatarsDAO.create({ ...req.body, id: void 0, domain: usr.domain })(trx);
          return avatarsDAO.read(pk)(trx);
        })()
    )(req);
  }

  function putAvatar(req: Request) {
    return withParsedRequest(GetAvatarRequest, ({ params }) =>
      withAuthorizedUser("avatar_put",
        async (usr) => withTransaction(async (trx) => {
          const
            id = params.avatar_id,
            domain_id = usr.domain.id;
          if (await isInDomain(id, domain_id)(trx) && await avatarsDAO.update({ id }, { ...req.body })(trx) !== 0)
            return avatarsDAO.read({ id })(trx);
          else
            return void 0; // Respuesta será 404 Not found 

        })()
      )(req)
    )(req);
  }
  function deleteAvatar(req: Request) {
    return withParsedRequest(GetAvatarRequest, ({ params }) =>
      withAuthorizedUser("avatar_put",
        async (usr) => withTransaction(async (trx) => {
          const
            id = params.avatar_id,
            domain_id = usr.domain?.id ?? throwUndefinedError("Unexpected:  readed user without domain");
          if (await isInDomain(id, domain_id)(trx) && await avatarsDAO.remove({ id })(trx) !== 0)
            return { id } as AvatarPkDto;
          else
            return void 0;  // Convertido a 404 Not Found
        })()
      )(req)
    )(req);
  }
  function isInDomain(id: number, domain_id: number) {
    return async (cli: PoolClient) => 0 !== await avatarsDAO.count({ id, domain_id })(cli);
  }
}


//#region estructuras de datos con las que parseamos params/query/body
class GetAvatarParams {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  avatar_id!: number
}
@jsonObject
class GetAvatarRequest {
  @jsonMember(GetAvatarParams, { isRequired: true })
  params!: GetAvatarParams
}
@jsonObject
class GetAvatarsQuery {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  domain_id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  page_offset?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer() })
  page_limit?: number
}
@jsonObject
class GetAvatarsRequest {
  @jsonMember(GetAvatarsQuery, { isRequired: true })
  query!: GetAvatarsQuery
}
///#region
