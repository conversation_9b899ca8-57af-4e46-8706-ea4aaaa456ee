import { Db<PERSON>rudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { IContainer_db, IContainer_logger } from "container/IContainer";
import * as pg from "pg";
import { WAContactDto, WAContactPKDto } from "whatsapp-srv-dtos";
import { IWAContactsDAO, WAContactsFilter } from "../IWAContactsDAO";
import { row2DTO } from "./row2dto";
import { waAccountsFields } from "./schema/tables/waAccountsTable";
import { waContactsFields, waContactsTable } from "./schema/tables/waContactsTable";

const { insertRecord, updateRecord } = DbCrudUtils;
const { buildSqlFields, buildSqlFrom } = SqlHelpers;
const { addEq, addNotEq, addIn } = SqlWhereHelpers;

export function WAContactsDAO({ db, dbSchema, logger }: IContainer_db & IContainer_logger): IWAContactsDAO {
  const flds = waContactsFields;
  const alias = "wac"

  return {
    list,
    upset,
    read,
  };

  function list(filter: WAContactsFilter) {

    const
      offset = filter.page_offset ?? 0,
      limit = filter.page_limit ?? 100;
    let values: any[] = [];
    let terms: string[] = ["true"];
    let fromPaths = new Set<string>([alias]);
    let fieldsPaths = new Set<string>([alias]);
    let disallowedFieldPaths = new Set<string>([]);

    addEq(terms, values, `${alias}.${flds.account_id}`, filter.account_id);
    addEq(terms, values, `${alias}.${flds.id}`, filter.id);
    addIn(terms, values, `${alias}.${flds.account_id}`, filter.account_ids);
    addIn(terms, values, `${alias}.${flds.id}`, filter.ids);
    addEq(terms, values, `${alias}.${flds.isMe}`, filter.isMe);
    if (filter.includeAccountDetails) {
      fromPaths.add(`${alias}_account`);
      fieldsPaths.add(`${alias}_account`);
    }
    if (filter.account_avatar_id !== void 0 || filter.account_enabled !== void 0 || filter.account_historic !== void 0) {
      fromPaths.add(`${alias}_account`);
      addEq(terms, values, `${alias}_account.${waAccountsFields.avatar_id}`, filter.account_avatar_id);
      addEq(terms, values, `${alias}_account.${waAccountsFields.enabled}`, filter.account_enabled);
      addNotEq(terms, values, `${alias}_account.${waAccountsFields.enabled}`, filter.account_historic);
    }
    if (filter.includeMeta === false)
      disallowedFieldPaths.add(`${alias}_${waContactsFields.meta}`);

    if (filter.phonenumber) {
      let normalized = filter.phonenumber.replace(/[^\d]/g, '');
      if (normalized.length !== 0) {
        addEq(terms, values, `${alias}.${flds.id}`, normalized);
        // @deprecated: Los identificadores de contacto son sus números de telefono segido de "@" y el servidor (ej: c.us)
        //addBeginsLike(terms, values, `wac.${flds.id}`, normalized + "@");
      }
    }
    const qryOptions = {
      table: waContactsTable,
      tableAlias: alias
    };
    const
      qryFields = buildSqlFields(dbSchema, { ...qryOptions, allowedPaths: fieldsPaths, disallowedPaths: disallowedFieldPaths }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, { ...qryOptions, allowedPaths: fromPaths });
    const
      qry = `select ${qryFields} from ${qryFrom} where ${terms.join(" and ")} offset ${offset} limit ${limit}`;

    return db.withClient(async dbCli => {
      const rows = await dbCli.query(qry, values).then(result => result.rows);
      return rows.map(row => row2DTO.toWAContact(row, alias));
    });
  }

  function upset(data: WAContactDto): (dbCli?: pg.PoolClient) => Promise<void> {
    return db.withTransaction(async trx => {
      const n = await updateRecord(waContactsTable, data)(trx);
      if (n === 0)
        await insertRecord(waContactsTable, data)(trx);
    });
  }

  function read(pk: WAContactPKDto): (dbCli?: pg.PoolClient) => Promise<WAContactDto | undefined> {
    return db.withClient(async cli => {
      const [contact] = await list({ account_id: pk.account.id, id: pk.id })(cli);
      return contact;
    });
  }


}