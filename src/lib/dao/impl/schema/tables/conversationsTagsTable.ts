import { DBSchema } from "agentor-lib";

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum conversationsTagsFields {
  conversation_avatar_id = "conversation_avatar_id",
  conversation_contact_id = "conversation_contact_id",
  tag_id = "tag_id",
}

const fldN = conversationsTagsFields;

export const conversationsTagsTable = new DBSchema.TableSchema(
  "conversations_tags",
  [
    new DBSchema.TableFieldSchema(fldN.conversation_avatar_id, { c, r, nn, pk }),
    new DBSchema.TableFieldSchema(fldN.conversation_contact_id, { c, r, nn, pk }),
    new DBSchema.TableFieldSchema(fldN.tag_id, { c, r, nn, pk }),
  ],
  [
    new DBSchema.TableFKSchema("conversation"),
    new DBSchema.TableFKSchema("tag"),
  ]
);