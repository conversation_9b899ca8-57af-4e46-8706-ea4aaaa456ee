import { DBSchema } from "agentor-lib";

const [c, r, u, pk, nn] = [true, true, true, true, true];

export const apikeysFields = {
  key: "key",
  user_id: "user_id"
  //account_id: "account_id"
}
export const apikeysTable = new DBSchema.TableSchema(
  "apikeys",
  [
    new DBSchema.TableFieldSchema(apikeysFields.key, { c, r, pk, nn }),
    new DBSchema.TableFieldSchema(apikeysFields.user_id, { c, r, nn }),
  ],
  [
    new DBSchema.TableFKSchema("user", "users")
  ]
);
