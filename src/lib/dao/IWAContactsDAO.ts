import { ConversationDto, ConversationPKDto, WAContactDto, WAContactPKDto } from "whatsapp-srv-dtos";
import * as pg from "pg";

export type WAContactsFilter = {
  account_id?: string,
  account_ids?: string[],
  account_avatar_id?: number,
  account_enabled?: boolean,
  account_historic?: boolean,
  id?: string,
  ids?: string[],
  phonenumber?: string,
  isMe?: boolean,
  includeMeta?: boolean,
  includeAccountDetails?: boolean,
  page_offset?: number,
  page_limit?: number,
}
export interface IWAContactsDAO {
  list(filter: WAContactsFilter): (dbCli?: pg.PoolClient) => Promise<WAContactDto[]>;
  upset(data: WAContactDto): (dbTrx?: pg.PoolClient) => Promise<void>;
  read(pk: WAContactPKDto): (dbCli?: pg.PoolClient) => Promise<WAContactDto | undefined>;
}

