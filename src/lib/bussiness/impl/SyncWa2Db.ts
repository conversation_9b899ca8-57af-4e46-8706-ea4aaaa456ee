import { Db } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { WAContactDto } from "whatsapp-srv-dtos";
import { WAWebJS2Dto } from "./common/WAWebJS2Dto";


export function SyncWa2Db({ db: { withTransaction }, wacontactsDAO, wamessagesDAO, waclients, logger, loggerUtils }: IContainer) {

  return {
    syncContacts,
    syncContactByNumber,
    syncContactByNumberIfNotExists,
    syncChats,
  };

  /** Sincroniza los contactos con los de whatsapp */
  async function syncContacts(accountNumber: string) {
    return loggerUtils.withDebug(`syncContacts(${accountNumber})`)(async () => {
      const contacts = await waclients.listContacts(accountNumber);
      for (const contact of contacts) {
        try {
          const contactDTO = WAWebJS2Dto.toWAContactDto(accountNumber, contact);
          await wacontactsDAO.upset(contactDTO)();
          logger.debug(`${accountNumber}: updated ${contact.id._serialized}`);
        } catch (e) {
          logger.error(`${accountNumber}: Problems upsetting contact ${contact.id}`, e);
          throw e;
        }
      }
    });
  }
  async function syncContactByNumber(accountNumber: string, contactNumber: string) {
    return loggerUtils.withDebug(`syncContactByNumber(${accountNumber},${contactNumber})`)(async () => {
      const contact = await waclients.getContact(accountNumber, contactNumber);
      if (contact) {
        const contactDTO = WAWebJS2Dto.toWAContactDto(accountNumber, contact);
        await wacontactsDAO.upset(contactDTO);
      }
    });
  }

  async function syncContactByNumberIfNotExists(accountNumber: string, contactNumber: string): Promise<WAContactDto | undefined> {
    return loggerUtils.withDebug(`syncContactByNumberIfNotExists(${accountNumber},${contactNumber})`)(async () => {
      const contactPK = { account: { id: accountNumber }, id: contactNumber };
      const wacontact = await wacontactsDAO.read(contactPK)();
      if (wacontact)
        return wacontact;
      else {
        let clientContact = await waclients.getContact(accountNumber, contactNumber);
        if (clientContact) {
          let wacontactData = WAWebJS2Dto.toWAContactDto(accountNumber, clientContact);
          return await withTransaction(async (trx) => {
            await wacontactsDAO.upset(wacontactData)(trx);
            return wacontactsDAO.read(contactPK)(trx)
          })()
        } else {
          return void 0;
        }
      }
    });
  }

  async function syncChats(account_phonenumber: string) {
    return loggerUtils.withDebug(`syncChats(${account_phonenumber})`)(async () => {
      const contacts = (await waclients.listContacts(account_phonenumber)).filter(c => !c.isGroup && !c.isMe && c.isUser);
      for (const contact of contacts) {
        await syncChat(account_phonenumber, contact.number)
      }
    });
  }
  async function syncChat(account_id: string, phonenumber: string) {

    logger.debug(`${account_id}: synchornizing ${phonenumber} chats`)
    const unknownMessages = listUnknownMessages(account_id, phonenumber);
    let unknownMessage = await unknownMessages.next();
    while (!unknownMessage.done) {
      const messageDTO = WAWebJS2Dto.toWAMessageDto(account_id, unknownMessage.value);
      try {
        await wamessagesDAO.upset(messageDTO)();
      } catch (e) {
        logger.error(`${account_id}: Cant upset message ${messageDTO}`, e)
      }
      unknownMessage = await unknownMessages.next();
    }

  }
  /** 
   * Enumerar 1 a 1 los mensajes que aún no conocemos en la BBDD.
   * ¡¡¡ OJO: Internamente se ve obligado a leer todos los mensajes del chat!!!
   */
  async function* listUnknownMessages(account_id: string, number: string) {
    const pageSize = 100;


    // Si hay algún problema obteniendo mensajes, 
    const messages = (await
      waclients.
        listMessages(account_id, number).
        catch(e => {
          logger.warn(`Can't read ${number} messages`, e);
          return [];
        })
    ).filter(message =>
      // Solo mensajes cuyo autor sea el origen... nos quitamos de en medio alertas.
      !message.author || message.author === message.from
    );

    // Los tratamos en bloques de <pageSize> mensajes;
    for (let n = 0; n < messages.length; n += pageSize) {
      let page = messages.slice(n, n + pageSize);
      const existingIds = await wamessagesDAO.listIds({ account_id: account_id, ids: page.map(m => m.id.id) })();
      if (existingIds.length !== page.length) {
        const idsSet = new Set<string>(existingIds);
        for (const msg of page.filter(m => !idsSet.has(m.id.id)))
          yield msg;
      }
    }
  }

}


