import { Db<PERSON>rudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { IContainer_db } from "container/IContainer";
import { WAAccountDto, WAAccountPkDto } from "whatsapp-srv-dtos";
import { IWAAccountsDAO, WAAccountsFilter } from "../IWAAccountsDAO";
import { row2DTO } from "./row2dto";
import { AvatarsFields } from "./schema/tables/avatarsTable";
import { waAccountsFields, waAccountsTable } from "./schema/tables/waAccountsTable";
const { insertRecord, updateRecord } = DbCrudUtils;
const { buildSqlFields, buildSqlFrom } = SqlHelpers;
const { addEq, addNotEq } = SqlWhereHelpers;

export function WAAccountsDAO({ db, dbSchema }: IContainer_db): IWAAccountsDAO {
  const flds = waAccountsFields;
  const alias = "a";

  return {
    list,
    create,
    read,
    update,
    //readByApikey,
  }


  function list(filter: WAAccountsFilter = {}) {
    var values: any[] = [];
    var terms: string[] = ["true"];

    addEq(terms, values, `${alias}.${flds.id}`, filter.id);
    addEq(terms, values, `${alias}.${flds.enabled}`, filter.enabled);
    addNotEq(terms, values, `${alias}.${flds.enabled}`, filter.historic);
    addEq(terms, values, `${alias}.${flds.avatar_id}`, filter.avatar_id);
    addEq(terms, values, `${alias}_avatar.${AvatarsFields.domain_id}`, filter.avatar_domain_id);
    // addIsFkOf(terms, values,
    //   // Clave foránea
    //   `${alias}.${flds.id}`, ApikeysTable.tablename, ApikeysTable.flds.account_id,
    //   // Valor de busqueda en tabla remota
    //   ApikeysTable.flds.key, filter.apikey
    // );

    const qryOptions = {
      table: waAccountsTable,
      tableAlias: alias,
    };
    const qryFromOptions = {
      ...qryOptions,
      // Foreign keys
      allowedPaths: [
        alias,
        ...filter.avatar_domain_id ? [`${alias}_avatar`]: []
      ]
    };
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryFromOptions);

    const qry = `select ${qryFields} from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli => {
      const rows = await dbCli.query(qry, values).then(result => result.rows);
      return rows.map(row => row2DTO.toWAAccount(row, alias))
    });
  }
  function read({ id }: WAAccountPkDto) {
    return db.withClient(async cli => {
      const [account] = await list({ id })(cli);
      return account;
    });
  }
  function update({ id }: WAAccountPkDto, data: WAAccountDto) {
    console.log({ id, data });
    return db.withTransaction(updateRecord(waAccountsTable, { ...data, id }));
  }
  function create(account: WAAccountDto) {
    return db.withTransaction(async trx => {
      const rw = await insertRecord(waAccountsTable, account)(trx);
      return row2DTO.toWAAccountPK(rw);
    });
  }

}

