import { DBSchema } from "agentor-lib";

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum waMessagesFields {
  conversation_avatar_id = "conversation_avatar_id",
  conversation_contact_id = "conversation_contact_id",

  account_id = "account_id",
  id = "id",
  /** Computado.  Equivale a account_id */
  from_account_id = "from_account_id",
  from_id = "from_id",
  /** Computado.  Equivale a account_id */
  to_account_id = "to_account_id",
  to_id = "to_id",
  /** Computado.  Equivale a account_id */
  other_account_id = "other_account_id",
  /** Generado.  equivale al from_id o to_id que no sea account_id */
  other_id = "other_id",
  /** Computado.  Equivale a account_id */
  me_account_id = "me_account_id",
  /** Computado. equivale a from_id o to_id que no sean other_id, es decir: me_id es el contacto asociado a la cuenta **/
  me_id = "me_id",
  /** Generado.  'out' o 'in' dependiendo de si el mensaje va dirigido a other_id o si el mensaje proviene de other_id */
  direction = "direction",
  stamp = "stamp",
  /** Generado. Es único dentro de un account_id. equivale a stamp (precisión de segundos), pero añadiendo "milisegundos" según el orden de inserción de los mensajes  */
  uniquestamp = "uniquestamp",
  body = "body",
  type = "type",
  insertionindex = "insertionindex",
  original = "original"
}

const fldN = waMessagesFields;

export const waMessagesTable = new DBSchema.TableSchema(
  "wamessages",
  [
    new DBSchema.TableFieldSchema(fldN.account_id, { c, r, pk, nn }),
    new DBSchema.TableFieldSchema(fldN.id, { c, r, pk, nn }),
    /** Campo generado internamente  */       
    new DBSchema.TableFieldSchema(fldN.conversation_avatar_id, { r, nn }),
    /** Campo generado internamente  */       
    new DBSchema.TableFieldSchema(fldN.conversation_contact_id, { r, nn }),
        
    new DBSchema.TableFieldSchema(fldN.from_account_id, { r, nn, computed: (tableAlias: string) => `${tableAlias}.${fldN.account_id}` }),
    new DBSchema.TableFieldSchema(fldN.from_id, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.to_account_id, { r, nn, computed: (tableAlias: string) => `${tableAlias}.${fldN.account_id}` }),
    new DBSchema.TableFieldSchema(fldN.to_id, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.other_account_id, { r, nn, computed: (tableAlias: string) => `${tableAlias}.${fldN.account_id}` }),
    /* Este campo no es computado, sino que es realmente un campo generado en la tabla que nos permite optimizar consultas por "other_i" */
    new DBSchema.TableFieldSchema(fldN.other_id, { r, nn }),
    new DBSchema.TableFieldSchema(fldN.me_account_id, { r, nn, computed: (tableAlias: string) => `${tableAlias}.${fldN.account_id}` }),
    new DBSchema.TableFieldSchema(fldN.me_id, { r, nn, computed: (tableAlias: string) => `CASE WHEN "${tableAlias}".${fldN.from_id}="${tableAlias}".${fldN.other_id} THEN "${tableAlias}".${fldN.to_id} ELSE "${tableAlias}".${fldN.from_id} END` }),
    new DBSchema.TableFieldSchema(fldN.direction, { r, nn }),
    new DBSchema.TableFieldSchema(fldN.stamp, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.uniquestamp, { r, nn }),
    new DBSchema.TableFieldSchema(fldN.body, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.type, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.insertionindex, { r, nn }),
    new DBSchema.TableFieldSchema(fldN.original, { c, r, u, nn }),
  ],
  [
    new DBSchema.TableFKSchema("conversation", "conversations", new Map([
      [waMessagesFields.conversation_avatar_id, "avatar_id"],
      [waMessagesFields.conversation_contact_id, "contact_id"]
    ])),
    new DBSchema.TableFKSchema("account", "waaccounts"),
    new DBSchema.TableFKSchema("from", "wacontacts", new Map([
      [waMessagesFields.account_id, "account_id"],
      [waMessagesFields.from_id, "id"]
    ])),
    new DBSchema.TableFKSchema("to", "wacontacts", new Map([
      [waMessagesFields.account_id, "account_id"],
      [waMessagesFields.to_id, "id"]
    ])),
    new DBSchema.TableFKSchema("me", "wacontacts", new Map([
      [waMessagesFields.account_id, "account_id"],
      [waMessagesFields.me_id, "id"]
    ])),
    new DBSchema.TableFKSchema("other", "wacontacts", new Map([
      [waMessagesFields.account_id, "account_id"],
      [waMessagesFields.other_id, "id"]
    ]))    
  ]
);