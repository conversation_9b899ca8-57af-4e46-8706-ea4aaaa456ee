import { TagDto, TagPkDto } from "whatsapp-srv-dtos";
import * as pg from "pg";

export type TagsFilter = {
  domain_id?: number
  id?: number,
}
export interface ITagsDAO {
  list(filter?: TagsFilter): (dbCli?: pg.PoolClient) => Promise<TagDto[]>;
  count(filter?: TagsFilter): (dbCli?: pg.PoolClient) => Promise<number>;
  create(data: TagDto): (dbTrx?: pg.PoolClient) => Promise<TagPkDto>;
  update(pk: TagPkDto, data: TagDto): (dbTrx?: pg.PoolClient) => Promise<number | undefined>;
  read(pk: TagPkDto): (dbCli?: pg.PoolClient) => Promise<TagDto | undefined>;
  remove(pk: TagPkDto): (dbTrx?: pg.PoolClient) => Promise<number | undefined>;
}