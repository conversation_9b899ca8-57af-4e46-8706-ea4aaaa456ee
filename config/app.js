const { env } = require("process");
const fs = require("fs");

const environment = (env.NODE_ENV === "production") ? "production" : "devel";

const config = {
  logger: importJsonFile(`${__dirname}/${environment}/logger.json`),
  http: importJsonFile(`${__dirname}/${environment}/http.json`),
  db: importJsonFile(`${__dirname}/${environment}/db.json`),
  waclients: importJsonFile(`${__dirname}/${environment}/waclients.json`),
  smtp: importJsonFile(`${__dirname}/${environment}/smtp.json`),
  jwt: importJsonFile(`${__dirname}/${environment}/jwt.json`),
  hooks: importJsonFile(`${__dirname}/${environment}/hooks.json`),
};

module.exports = config;

function importJsonFile(path) {
  return JSON.parse(fs.readFileSync(path));
}