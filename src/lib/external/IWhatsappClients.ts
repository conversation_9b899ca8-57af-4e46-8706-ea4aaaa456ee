import WAWebJS, { MessageContent } from 'whatsapp-web.js';
import { WhatsappClients } from "./impl/WhatsappClients";

/**
 * Gestiona un grupo de clientes whatsapp (basado en WhatsappWebJS)
 */
export interface IWhatsappClients {
  /**
   * Lanza el cliente whatsapp asociado al usuarios.
   * Si ya hay un cliente asociado al número (listo o en preparación) el método fallará.
   * Asegúrate de consultar **getClientStatus** primero (debe ser "unknown") antes de evocar este método
   * 
   * @param clientNumber 
   */
  bootstrapClient(clientNumber: string): Promise<void>
  /**
   * Destruye el cliente whatsapp asociado al usuario.
   * 
   * @param clientNumber 
   */
  shutdownClient(clientNumber: string): Promise<void>

  /**
   * Consulta el estado de un cliente
   * @param clientNumber 
   */
  getClientStatus(clientNumber: String): WhatsappClients.clientStatus

  /**
   * <PERSON>iza un snapshot (formato jpeg) de la página en la que está cargada la aplicación whatsapp del cliente
   * @param clientNumber 
   */
  getClientSnapshotAsJpeg(clientNumber: string): Promise<Buffer | undefined>

  /**
   * Enviar mensaje
   * @param clientNumber 
   * @param toNumber 
   * @param message 
   */
  sendMessage(clientNumber: string, toNumber: string, message: MessageContent): Promise<WAWebJS.Message>

  /**
   * Lista todos los mensajes intercambiados con el número de teléfono indicado (tanto enviados como recibidos)
   * @param clientNumber 
   * @param withNumber 
   */
  listMessages(clientNumber: string, withNumber: string): Promise<WAWebJS.Message[]>
  /**
   * Lista de todos los contactos
   * @param clientNumber 
   */
  listContacts(clientNumber: string): Promise<WAWebJS.Contact[]>
  /**
   * Obtener 1 contacto usando su número de teléfono
   * @param clientNumber 
   * @param contactNumber 
   */
  getContact(clientNumber: string, contactNumber: string): Promise<WAWebJS.Contact | undefined>
  /**
   * Usado para subscribirnos a la recpeción de mensajes de cualquier cliente
   */
  get emmiter(): WhatsappClients.IMessagesEmiter
  /**
   * Cerrar todos los clientes
   */
  dispose(): Promise<void>
}


export namespace IWhatsappClients {
  export class UnknownClientError extends Error { constructor(msg: string) { super(msg); Object.setPrototypeOf(this, UnknownClientError.prototype); } }
  export class UnitializedClientError extends Error { constructor(msg: string) { super(msg); Object.setPrototypeOf(this, UnitializedClientError.prototype); } }
  export class UnknownPhonenumberError extends Error { constructor(msg: string) { super(msg); Object.setPrototypeOf(this, UnknownPhonenumberError.prototype); } }
  export class UnknownContactError extends Error { constructor(msg: string) { super(msg); Object.setPrototypeOf(this, UnknownContactError.prototype); } }
  export class NoContactChatError extends Error { constructor(msg: string) { super(msg); Object.setPrototypeOf(this, NoContactChatError.prototype); } }
}