import { AvatarDto, AvatarPkDto, ContactDto, ContactPkDto, WAAccountDto, WAAccountPkDto } from "whatsapp-srv-dtos";
import * as pg from "pg";

export type ContactsFilter = {
  domain_id?: number,
  id?: number,
  phonenumber?: string
}
/**
 * Comandos de datos para la entidad Contact
 * 
 * Importante:  
 *   NO se incluyen métodos para la creación de contactos... La operación se realiza internamente por trigger al resgtistrar un nuevo wacontact
 *   ??? La alternativa "buena" sería crear una operación de negocio para crear un nuevo wacontact... esta operación crearía (si necesario) un contact
 */
export interface IContactsDAO {
  list(filter?: ContactsFilter): (dbCli?: pg.PoolClient) => Promise<ContactDto[]>;
  read(pk: ContactPkDto): (dbCli?: pg.PoolClient) => Promise<ContactDto | undefined>;
}