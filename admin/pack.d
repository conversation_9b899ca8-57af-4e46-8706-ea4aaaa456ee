#!/usr/bin/env rdmd
/++
 + <PERSON>ript implementado en lenguaje d
 + De<PERSON> tener instalado rdmd en tu sistema (acompaña a dmd)
 + $ sudo apt install dmd
 + o bien
 + $ sudo snap install dmd --classic
 +
 +
 + Compila y empaqueta los componentes necesarios para instalar el servicio en un ubutu linux de aws (en la carpeta home del usuario)
 + El resultado final son 2 ficheros:
 + installer/whatsappsrvYYYYMMDDhhmm.7z
 + installer/whatsappsrvYYYYMMDDhhmm.sh
 +/
import std.stdio : writeln;
import std.format : format;
import std.file : exists, copy, mkdirRecurse, rmdirRecurse;
import std.algorithm : each, map;

import admin.lib.tools : cleanupDir, copyDir, exec, generateZip;

void main()
{
  import admin.vars : c_installer_dir;

  try
  {
    auto releasePath = c_installer_dir ~ "/release";
    auto packadgeName = generatePackadgeName();
    auto zipFileName = packadgeName ~ ".7z";
    auto shFileName = packadgeName ~ ".sh";
    log("--------------------------------------");
    log("    Compile and generate installer    ");
    log("--------------------------------------");
    log("");
    log("Cleaning up installer folder");
    cleanupDir(c_installer_dir);
    // Realizamos compilación de librerías propias (locales) y movemos el resultado a la carpeta de instalación destino.
    ["agentor-lib", "whatsapp-srv-dtos"].each!((localLibName) {
      string localLibPath = "../" ~ localLibName;
      log(localLibName ~ ": compiling");
      // Obtener referencias (modo devel) y compilar.  
      runNpmInstall(localLibPath, false);
      runNpmBuild(localLibPath);
      // Copiar a la carpeta installer/release
      log(localLibName ~ ": copying to installer folder");
      mkdirRecurse(
        releasePath ~ "/" ~ localLibName
      );
      copyDir(
        localLibPath ~ "/lib",
        releasePath ~ "/" ~ localLibName
      );
      copy(
        localLibPath ~ "/package.json",
        releasePath ~ "/" ~ localLibName ~ "/package.json"
      );
    });

    // Obtener referencia (modo devel) y compilar)
    log("whatsapp-srv: compiling");
    runNpmInstall("./", false);
    runNpmBuild();
    // Copiar a la carpeta de installer/release.    
    log("whatsapp-srv: copying to installer folder");
    mkdirRecurse(releasePath ~ "/whatsapp-srv");
    copyDir(
      "build",
      releasePath ~ "/whatsapp-srv",
      true
    );
    copy(
      "package.json",
      releasePath ~ "/whatsapp-srv/package.json"
    );
    copy(
      "tsconfig.json",
      releasePath ~ "/whatsapp-srv/tsconfig.json"
    );

    // Configuración    
    log("whatsapp-srv conf: copying to installer folder");
    copyDir(
      "config",
      releasePath
    );
    // Otros "assets"
    log("whatsapp-srv assets: copying to installer folder");
    copyDir(
      "admin/assets",
      releasePath
    );

    // Generamos zip con los 4 elementos
    log("Generating zip file");
    generateZip(
      releasePath,
      c_installer_dir ~ "/" ~ zipFileName
    );
    // Removing temporal folder
    log("Removing temporal folder");
    rmdirRecurse(releasePath);

    log("Generating sh file");
    generateShFile(
      c_installer_dir ~ "/" ~ shFileName,
      zipFileName,
      packadgeName
    );

    log("Installer generated Ok");
  }
  catch (Exception e)
  {
    writeln("Installer has not been generated", e);
  }
  finally
  {
    log("");
  }
}

private:

string generatePackadgeName()
{
  import std.datetime.systime : Clock;

  const now = Clock.currTime();
  return format!"whatsappsrv%04d%02d%02d%02d%02d"(now.year, now.month,
    now.day, now.hour, now.minute);
}

void generateShFile(string shFilePath, string zipFileName, string packadgeName)
{
  import std.array : join;
  import std.stdio : File;

  const c_srv_name = "whatsapp-srv";

  scope (failure)
    writeln("Problems generating sh file");

  File(shFilePath, "w").write([
    format!"if ! node --version 2> /dev/null | grep -icq \"v16.20\"; then"(),
    format!"  echo \"Node v16.20 is not installed\""(),
    format!"  exit 1"(),
    format!"elif ! which 7z >/dev/null; then"(),
    format!"  echo \"7z is not installed\""(),
    format!"  exit 1"(),
    format!"elif [ -d %s ]; then"(packadgeName),
    format!"  echo \"Alredy installed\""(),
    format!"else"(),
    format!"  echo \"Aseguramos librerías de las que depende chromium (usado a través de puppeter) \""(),
    format!"  sudo apt install -y libatk1.0-dev libatk-bridge2.0-dev libcups2 libxbae-dev libxkbcommon-x11-0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libpango-1.0-0 libcairo2"(),
    format!"  restoresrv=0"(),
    format!"  restorecron=0"(),
    format!"  7z x %s -o%s"(zipFileName, packadgeName),
    ["agentor-lib", "whatsapp-srv-dtos", "whatsapp-srv"].map!((project) =>
        [
          format!""(),
          format!"  # install %s dependencies"(project),
          format!"  cd %s/%s"(packadgeName, project),
          format!"  npm install --production"(),
          format!"  cd ../.."(),
        ].join("\n")
    ).join("\n"),
    format!""(),
    format!"  cp -rf %s/assets/whatsapp-srv/node_modules/* %s/whatsapp-srv/node_modules/"(packadgeName, packadgeName),
    format!"  if sudo systemctl status %s.service 2> /dev/null | grep -q -wi running; then"(c_srv_name),
    format!"    echo \"stopping service %s\""(c_srv_name),
    format!"    sudo service %s stop"(c_srv_name),
    format!"    restoresrv=1"(),
    format!"  fi"(),
    format!"  if sudo systemctl status cron.service 2> /dev/null | grep -q -wi running; then"(),
    format!"    echo \"stopping service %s\""("cron"),
    format!"    sudo service cron stop"(),
    format!"    restorecron=1"(),
    format!"  fi"(),
    format!"  # Redirigimos el link simbólico a la nueva versión"(),
    format!"  ln -fsn %s whatsappsrv"(packadgeName),
    format!"  # Restauramos los servicios que se pararon"(),
    format!"  if [ $restorecron -eq 1 ]"(),
    format!"  then"(),
    format!"    echo \"restarting cron\""(),
    format!"    sudo service cron start"(),
    format!"  fi"(),
    format!"  if ! sudo systemctl list-units 2> /dev/null | grep -q -wic \"%s.service\"; then"(c_srv_name),
    format!"    # Si el servicio no está instalado "(),
    format!"    echo \"Installing %s service\""(c_srv_name),
    format!"    sudo cp whatsappsrv/assets/etc/systemd/system/%s.service /etc/systemd/system"(
      c_srv_name),
    format!"    sudo systemctl enable %s.service"(c_srv_name),
    format!"    sudo service %s start"(c_srv_name),
    format!"  elif ! cmp -s \"whatsappsrv/assets/etc/systemd/system/%s.service\" \"/etc/systemd/system/%s.service\"; then"(
      c_srv_name, c_srv_name),
    format!"    # Si el servicio está instalado, pero ha cambiado "(),
    format!"    echo \"Re-installing %s service\""(c_srv_name),
    format!"    sudo cp whatsappsrv/assets/etc/systemd/system/%s.service /etc/systemd/system"(c_srv_name),
    format!"    sudo systemctl daemon-reload"(),
    format!"  fi"(),
    format!"  if [ $restoresrv -eq 1 ]"(),
    format!"  then"(),
    format!"    # Si el servicio estaba iniciado anteriormente, lo iniciamos"(),
    format!"    echo \"restarting %s service\""(c_srv_name),
    format!"    sudo service %s start"(c_srv_name),
    format!"  fi"(),
    format!"  if [ ! -f /etc/logrotate.d/%s ]; then "(c_srv_name),
    format!"    sudo cp whatsappsrv/assets/etc/logrotate.d/%s /etc/logrotate.d"(c_srv_name),
    format!"  fi"(),
    format!"fi"()
  ].join("\n"));
  format!"chmod +x \"%s\""(shFilePath).exec();
}

void runNpmBuild(string folderPath = "./")
in (folderPath.exists, format!"%s folder doesn't exist"(folderPath))
{
  import std.file : chdir, getcwd;

  scope (failure)
    format!"Problems building %s"(folderPath.exists);

  auto actualDir = getcwd();
  scope (exit)
    chdir(actualDir);

  chdir(folderPath);
  exec("npm run build");
}

void runNpmInstall(string folderPath = "./", bool production = true)
in (folderPath.exists, format!"%s folder doesn't exist"(folderPath))
{
  import std.file : chdir, getcwd;

  scope (failure)
    format!"Problems running npm install %s"(folderPath.exists);

  auto actualDir = getcwd();
  scope (exit)
    chdir(actualDir);

  chdir(folderPath);
  exec("npm install" ~ (production ? "--production" : ""));
}

void log(string txt)
{
  writeln("pack.d> " ~ txt);
}
