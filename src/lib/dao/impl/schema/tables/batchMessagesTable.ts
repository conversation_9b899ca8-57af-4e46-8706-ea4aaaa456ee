import { DBSchema } from "agentor-lib";



export enum batchMessagesFields {
  task_id = "task_id",
  id = "id",
  from_number = "from_number",
  to_number = "to_number",
  content_body = "content_body",
  content_type = "content_type",
  status_code = "status_code",
  triescount = "triescount",
  sentmessage_account_id = "sentmessage_account_id",
  sentmessage_id = "sentmessage_id",
  error_code = "error_code",
  error_text = "error_text",
}

const fldN = batchMessagesFields;
const [c, r, u, pk, nn] = [true, true, true, true, true];

export const batchMessagesTable: DBSchema.TableSchema = new DBSchema.TableSchema(
  "bat_messages",
  [
    new DBSchema.TableFieldSchema(fldN.task_id, { c, r, pk, nn }),
    new DBSchema.TableFieldSchema(fldN.id, { c, r, pk, nn }),
    new DBSchema.TableFieldSchema(fldN.from_number, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.to_number, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.content_body, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.content_type, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.status_code, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.triescount, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.error_code, { c, r, u }),
    new DBSchema.TableFieldSchema(fldN.error_text, { c, r, u }),
    new DBSchema.TableFieldSchema(fldN.sentmessage_account_id, { c, r, u }),
    new DBSchema.TableFieldSchema(fldN.sentmessage_id, { c, r, u }),
  ],
  [
    new DBSchema.TableFKSchema("task", "bat_tasks"),
    new DBSchema.TableFKSchema("sentmessage", "wamessages")

  ]
);