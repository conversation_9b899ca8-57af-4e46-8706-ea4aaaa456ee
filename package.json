{"name": "whatsapp-srv", "version": "1.2.02", "description": "Servicio para interactuar con cuentas Whatsapp", "main": "src/index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "ts-node -r tsconfig-paths/register src/index.ts", "build": "npm run clean && tsc && cp basecontent/* build && npm run cp_json_to_build && cp package.json build", "clean": "rm -rf ./build", "cp_json_to_build": "rsync -a -m --include '*/' --include '*.json' --include '*.png' --exclude '*' ./src/lib/controllers/api/schemas/ ./build/lib/controllers/api/schemas/"}, "author": "topbrokers", "license": "ISC", "dependencies": {"agentor-lib": "file:../agentor-lib", "ajv": "^8.11.0", "ajv-formats": "^2.1.1", "axios": "^1.5.1", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.17.1", "lodash": "^4.17.21", "node-fetch": "^3.3.1", "pg": "^8.11.3", "qrcode": "^1.5.0", "tsconfig-paths": "^3.14.1", "typedjson": "^1.8.0", "whatsapp-srv-dtos": "file:../whatsapp-srv-dtos", "whatsapp-web.js": "^1.31.0", "winston": "^3.6.0"}, "devDependencies": {"@types/ajv": "^1.0.0", "@types/axios": "^0.14.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/express": "^4.17.20", "@types/lodash": "^4.14.182", "@types/node": "^14.6.2", "@types/node-fetch": "^2.6.4", "@types/pg": "^8.11.3", "@types/qrcode": "^1.4.2", "ts-node": "^10.9.1", "typescript": "^4.7.4"}}