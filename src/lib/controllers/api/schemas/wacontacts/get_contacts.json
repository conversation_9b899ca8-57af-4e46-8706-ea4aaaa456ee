{"type": "object", "properties": {"query": {"type": "object", "properties": {"account_id": {"$ref": "/common/notempty_integer_string"}, "account_ids": {"type": "array", "items": {"$ref": "/common/notempty_integer_string"}}, "id": {"$ref": "/common/notempty_integer_string"}, "ids": {"type": "array", "items": {"$ref": "/common/notempty_integer_string"}}, "isMe": {"$ref": "/common/notempty_boolean_string"}, "includeMeta": {"$ref": "/common/notempty_boolean_string"}, "includeAccountDetails": {"$ref": "/common/notempty_boolean_string"}, "page_offset": {"$ref": "/common/notempty_integer_string"}, "page_limit": {"$ref": "/common/notempty_integer_string"}, "orderby": {"enum": ["key_asc", "key_desc", "name_asc", "name_desc"]}}, "required": [], "additionalProperties": false}}, "required": ["query"]}