import { Db<PERSON>rudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { IContainer_db } from "container/IContainer";
import { ConversationtageventDto, ConversationtageventStructDto, ConversationtagPkDto, ConversationtagStructDto, ConvTagOpCode } from "whatsapp-srv-dtos";
import { ConversationstageventsFilter, IConversationstageventsDAO } from "../IConversationsTageventsDAO";
import { ConversationstagsFilter, IConversationstagsDAO } from "../IConversationsTagsDAO";
import { row2DTO } from "./row2dto";
import { AvatarsFields } from "./schema/tables/avatarsTable";
import { conversationsTageventsFields, conversationsTageventsTable } from "./schema/tables/conversationsTageventsTable";
import { conversationsTagsFields, conversationsTagsTable } from "./schema/tables/conversationsTagsTable";
import { tagsFields } from "./schema/tables/tagsTable";

const
  { insertRecord, updateRecord, removeRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers,
  { addEq } = SqlWhereHelpers
  ;

export function ConversationtageventsDAO({ db, dbSchema }: IContainer_db): IConversationstageventsDAO {
  const flds = conversationsTageventsFields;
  const alias = "cvtgev";

  return {
    list,
    count,
   
  };
  function list(filter: ConversationstageventsFilter = {}) {
    const { values, terms, fromPaths, fieldsPaths } = buildConditions(filter);
    const
      qryOptions = {
        table: conversationsTageventsTable,
        tableAlias: alias,
      };
    const
      qryFields = buildSqlFields(dbSchema, { ...qryOptions, allowedPaths: fieldsPaths }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, { ...qryOptions, allowedPaths: fromPaths });
    const
      qry = `select ${qryFields} from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli => {
      const rows = await dbCli.query(qry, values).then(result => result.rows);
      return rows.map(row => row2DTO.toConversationTagevent(row, alias))
    });
  }
  function count(filter: ConversationstagsFilter = {}) {
    const { values, terms, fromPaths } = buildConditions(filter);
    const
      qryFromOptions = {
        table: conversationsTageventsTable,
        tableAlias: alias,
        allowedPaths: fromPaths
      };
    const
      qryFrom = buildSqlFrom(dbSchema, qryFromOptions);

    const qry = `select count(*) as "howMany" from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli =>
      dbCli.query(qry, values).then(
        result => parseInt(result.rows[0]["howMany"])
      )
    );
  }


  function buildConditions(filter: ConversationstagsFilter = {}) {
    var values: any[] = [];
    var terms: string[] = ["true"];
    var fromPaths: Set<string> = new Set([alias]); // Qué paths deben resolver las joins en el from
    var fieldsPaths: Set<string> = new Set([alias]);

    addEq(terms, values, `${alias}.${flds.conversation_avatar_id}`, filter.conversation_avatar_id);
    addEq(terms, values, `${alias}.${flds.conversation_contact_id}`, filter.conversation_contact_id);
    addEq(terms, values, `${alias}.${flds.tag_id}`, filter.tag_id);
    if (filter.domain_id !== void 0) {
      // La condición sobre el dominio la hacemos sobre el tag. 
      addEq(terms, values, `${alias}_tag.${tagsFields.domain_id}`, filter.domain_id);
      fromPaths.add(`${alias}_tag`);
    }
    
    if (filter.detail_conversation_avatar) {
      fromPaths.add(`${alias}_conversation_avatar`);
      fieldsPaths.add(`${alias}_conversation_avatar`);
    }
    if (filter.detail_conversation_contact) {
      fromPaths.add(`${alias}_conversation_contact`);
      fieldsPaths.add(`${alias}_conversation_contact`);
    }
    if (filter.detail_tag) {
      fromPaths.add(`${alias}_tag`);
      fieldsPaths.add(`${alias}_tag`);
    }

    return {
      values,
      terms,
      // Paths que deben ser resueltos por el From
      fromPaths: [...fromPaths.values()],
      // Paths que deben ser resueltos por el Select
      fieldsPaths: [...fieldsPaths.values()]
    };
  }
}

