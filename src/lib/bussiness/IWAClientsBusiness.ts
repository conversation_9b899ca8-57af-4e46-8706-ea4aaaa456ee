import { WhatsappClients } from "lib/external/impl/WhatsappClients";
import { WAMessageDto } from "whatsapp-srv-dtos";
import { MessageContent } from "whatsapp-web.js";

/**
 * Manages sending/receiving messages through whatsapp clients (waclients) reflecting the status in the DB
 *   
 * The dependent entities are:
 *   waaccounts: These are the whatsapp accounts registered in the DB. They can be deactivated. For each active account, we maintain an active waclient
 *   wacontacts: These are the whatsapp contacts with whom messages are exchanged. Each waaccount has its own set of contacts.
 *   wamessages: These are the sent/received messages. Each waaccount has its own set of wamessages
 * 
 */
export interface IWAClientsBusiness {
  /** Current status of a client */
  getClientStatus(client_id: string): WhatsappClients.clientStatus
  /** Screenshot of a client */
  getClientSnapshotAsJpeg(client_id: string): Promise<Buffer | undefined>
  /**
   * Initializes the whatsapp clients system and the processes in charge of:
   *   - Starting/stopping clients according to their status in the DB
   *   - Synchronizing contacts and messages in the background (In case we missed something :-)
   */
  bootstrap(): Promise<void>

  /**
   * Sends a message and records it in the repository (WaMessage entity).
   * If it is the first time contacting the recipient, their whatsapp data is obtained and a record is created
   * in the repository (WaContact entity)
   * @param client_id Client identifier (identified by phone number)
   * @param to Destination phone number
   * @param content The content to be sent (WhatsappWeb data structure)
   * @returns Sending status and, in case of success, the message object representing the sent message 
   */
  send(client_id: string, to: string, content: MessageContent): Promise<IWAClientsBusiness.sendResult>

  //batchSend(client_id: string, messages:BatchmessageDTO[]):Promise<void>

  /**
   * Ends all background tasks
   */
  dispose(): Promise<void>
}

export namespace IWAClientsBusiness {

  /**
   * Result code OK.
   * Development NOTE: MUST NOT coincide with any of the Error codes (sendErrorCode)
   */
  export enum sendResultCode {
    ok = "ok"
  }
  export enum sendErrorCode {
    err_unknown_client = "err_unknown_client",
    err_client_disabled = "err_client_disabled",
    err_unknown_contact = "err_unknown_contact",
    err_no_chat = "err_no_chat",
    err_other = "err_other"
  }
  export type sendResult = {
    code: sendResultCode,
    sentMessage: WAMessageDto
  } | {
    code: sendErrorCode,
  }
} 