-- El dominio
--   Nuestro servicio de whatsapp debe ser multidominio (un dominio = una "empresa").  Cada dominio tiene sus usuarios, avatares, cuentas de whatsapp, ...
-- El Avatar
--   Un avatar agrupa varias cuentas de whatsapp. Un avatar se define en un dominio
-- El Contacto
--   Contacto único por dominio.
--   Representa a todos los contactos de whatsapp que se repiten entre cuentas de whatasapp.
--   Se crea automáticamente (cuando se crea un wacontact se revisa si existe el contacto equivalente en el mismo dominio)
-- Conversation
--   Realación entre un Avatar y un Contact de un mismo dominio.
--   Se crea automáticamente (cuando se crea un wacontact relacionado con un waaccount se revisa si existe la conversación equivalente)
-- Tags
--   Los distintos tags definidos en un dominio y que pueden ser añadidos/quitados a una conversación.
-- ConversationTag
--   Cada uno de los tags añadidos a una conversación
-- ConversationTagevent
--   Registro de cada vez que un tag ha sido añadido/quitado de una conversación.
DO $$
DECLARE
  new_version integer:= 7;
  -- Dominio "topbrokers", usamos el entero más alto para evitar conflictos con serial id
  _tbdomain_id integer:= **********;
BEGIN
  IF (select max(version) from schema_changes) = (new_version - 1) THEN
    /* BEGIN */
    create table domains (
      id serial NOT NULL,
      name character varying NOT NULL,
      primary key (id )
    );
    comment on table domains is '
      Dominios de definición (una empresa, por ejemplo) a los que pertenecen los usuarios, avatares, los contactos, los tags....
    ';
    insert into domains (id, name) values (_tbdomain_id, 'topbrokers');
  
    alter table users
      add column domain_id integer not null default **********;
  
    -- El default solo era para asignar un valor inicial a los usuarios actuales... no debe estar en la versión definitiva.
    alter table users
      alter column domain_id DROP DEFAULT;
    alter table users
      add foreign key (domain_id) references domains (id) on delete cascade on update restrict;
    comment on column users.domain_id is '
      Dominio en el que actúa un usuario.  Un usuario solo puede actuar sobre entidades de su dominio (ej: cuentas/mensajes/tags/avatares de su dominio)
    ';
    
    create table avatars (
      id serial NOT NULL,
      domain_id integer NOT NULL,
      name character varying,
      primary key (id ),
      foreign key (domain_id ) references domains (id ) on delete cascade on update restrict
    );
    comment on table public.avatars is '
      Avatar:  Entidad lógica asociada a una o más cuentas de whatsapp.  
      Cuando enviamos un mensaje a través de un avatar, se elige la cuenta asociada activa.
      * En caso de haber varias elegiremos la que ya haya sido usada previamente para contactar con el contacto.
      * En caso de no haber ninguna, el avatar no es elegible.';
    alter table waaccounts
      ADD COLUMN avatar_id integer;
    -- not null más abajo tras asociar todas las cuentas con sus avatares
    comment on column public.waaccounts.avatar_id is 'Avatar al que se asocia la cuenta de whatsapp.';
    -- Creamos los avatares actuales.
    insert  into avatars (id, domain_id, name)
      values 
        (**********, _tbdomain_id, 'Gestión de Compradores'), 
        (**********, _tbdomain_id, 'Gestion de Propietarios'), 
        (**********, _tbdomain_id, 'Testing');
    -- En producción necesitamos asociar correctamente las cuentas actuales.
    update waaccounts set avatar_id = ********** where id in ('***********', '***********', '***********', '***********');
    update waaccounts set avatar_id = ********** where id in ('***********');
    update waaccounts set avatar_id = ********** where avatar_id is NULL;
    
    -- Ya tenemos todo asignado, hacemos que el avatar sea un caompo obligatorio y tenga integridad referencial con "avatars"
    alter table waaccounts
      alter column avatar_id set NOT NULL;
    alter table waaccounts
      add foreign key (avatar_id) references avatars (id) ON update RESTRICT ON DELETE CASCADE;
    comment on column waaccounts.avatar_id is '
      Avatar al que pertenece la cuenta de whatsapp.
      No es necesario incorporar este campo (avatar_id) a la clave primaria de waaccounts, ya que el número de teléfono
      de un waaccount (su id) es único en toda la instalación  (entre dominios, entre avatares)
    ';
    --
    create table tags (
      id serial not null,
      domain_id integer not null,
      label character varying not null,
      primary key ( id ),
      foreign key ( domain_id ) references domains (id ) on delete cascade on update restrict
    );
    comment on table tags is '
      Cada uno de los tags que podemos asignar a una conversación
    ';
    
    create table contacts
    (
      id serial not null,
      domain_id integer,
      phonenumber character varying not null,
      name character varying,
      primary key (id),
      unique (domain_id, phonenumber),
      foreign key (domain_id) references domains(id) on update restrict on delete cascade
    );

    comment on table contacts is '
      Representa a un contacto de la empresa representada en el dominio.  
      Es una entidad "calculada" (a través de trigger) que refleja los contactos (wacontacts) de todas las cuentas waaccounts en nuestro dominio  pero de forma única (si aparece en 3 cuentas, solo hay 1 contacto).
    ';

    comment on column contacts.phonenumber is '
      Teléfono en formato normalizado.  Al crear el contacto se recoge del campo waaccount.id
    ';

    insert into contacts(domain_id, phonenumber, name) 
      select _tbdomain_id as domain_id, wacontacts.id as phonenumber, max(wacontacts.name) from wacontacts group by 1,2;
    
    -- Cada una de las conversaciones existentes (tag<->contact).
    --   contact_id no es clave foránea, ya que los contactos son locales a cada cuenta... se refiere a cualquier contacto de cualquier
    --   cuenta asociada al avatar que tenga este id.
    create table conversations (
      avatar_id integer NOT NULL,
      contact_id integer NOT NULL,
      primary key (avatar_id, contact_id ),
      foreign key (avatar_id ) references avatars (id ) on delete cascade on update restrict
    );
    comment on table conversations is '
      Conversación etre un avatar y un contacto.  No hay clave foranea a contacto, porque la tabla de contactos es dependiente de la tabla de cuentas y puede estarse repitriendo N veces.  TODO: Unificar una únia tabla de contactos y no relacionarla con las cuentas.
    ';
    --
    -- Creamos un trigger que registre una nueva conversación por cada contacto asociado a una cuenta (si no existe ya)
    --
    CREATE OR REPLACE FUNCTION wacontacts_ensure_contact_and_conversation ( )
      RETURNS TRIGGER AS $func$
    DECLARE
      _avatar_id integer;
      _domain_id integer;
      _contact_id integer;
    BEGIN
      select avt.domain_id, avt.id into _domain_id, _avatar_id  
      from 
        waaccounts wa 
        inner join avatars avt on wa.avatar_id=avt.id 
      where
        wa.id=NEW.account_id;
      
      IF (NOT _avatar_id is NULL) THEN
        select id into _contact_id from contacts where phonenumber=NEW.id and domain_id=_domain_id;
        if (_contact_id is null) THEN
          insert into contacts (
            domain_id, 
            phonenumber, 
            name 
          ) values ( 
            _domain_id, 
            NEW.id, 
            NEW.name 
          ) returning id into _contact_id;
        else
          update contacts set 
            name=NEW.name 
          where id=_contact_id;
        end if;

        -- Nos aseguramos de que hay una conversación entre el contacto y el avatar 
        IF (select count(*) from conversations where avatar_id = _avatar_id AND contact_id = _contact_id) = 0 THEN
          insert into conversations (
            avatar_id, 
            contact_id
          ) values (
            _avatar_id, 
            _contact_id
          ); 
        END IF;

      ELSE
        RAISE EXCEPTION 'Unexpected: An account without avatar!!!';
      END IF;
      RETURN NEW;
    END;
    $func$
    LANGUAGE 'plpgsql';
    
    CREATE TRIGGER wacontacts_insert_trigger
      BEFORE insert ON wacontacts
      FOR EACH ROW
      EXECUTE PROCEDURE wacontacts_ensure_contact_and_conversation ( );

    --
    -- Establecemos todas las conversaciones que deben existir... a partir de aquí, el trigger wacontacts_ensure_contact_and_conversation se encarga de todo
    --
    insert into conversations (avatar_id, contact_id)
      select DISTINCT
        avt.id,
        ct.id
      from
        wacontacts wct
        inner JOIN waaccounts wa ON wct.account_id = wa.id
        inner join avatars avt on wa.avatar_id=avt.id
        inner join contacts ct on wct.id=ct.phonenumber and avt.domain_id=ct.domain_id;


    -- Tags asociados a la conversación entre un avatar y un contacto a la que denominamos "chat".
    -- El término "contacto" se refiere a cualquier contacto de cualquier cuenta asociada al avatar que tenga el identificador indicado (puede estar en varias cuentas)...
    --   Dado que los contactos se repiten por cuenta, no podemos establecer una clave foránea (quizás sí en el futuro: unificar contactos y relacionar m-n con las cuentas... pero de momento no)
    create table conversations_tags (
      avatar_id integer NOT NULL,
      contact_id integer not null,
      tag_id integer NOT NULL,
      primary key (avatar_id, contact_id, tag_id ),
      foreign key (avatar_id, contact_id ) references conversations (avatar_id, contact_id ) on delete cascade on update restrict,
      foreign key (tag_id ) references tags (id ) on delete cascade on update restrict
    );
    comment on table conversations_tags is '
      Cada uno de los tags asignados a una conversación
    ';
    
    create table conversations_tagevents (
      id bigserial NOT NULL,
      avatar_id integer NOT NULL,
      contact_id integer NOT NULL,
      tag_id integer NOT NULL,
      at timestamptz NOT NULL DEFAULT now( ),
      op_code character varying NOT NULL CHECK (op_code = 'c' OR op_code = 'd' ),
      --primary key (avatar_id, contact_number, tag_id, timestamp)
      primary key (id ),
      foreign key (avatar_id, contact_id ) references conversations (avatar_id, contact_id ) on delete cascade on update restrict,
      foreign key (tag_id ) references tags (id ) on delete cascade on update restrict
    );
    comment on table conversations_tagevents is 'Cada evento de añadir/quitar un tag a una conversación';
    comment on column conversations_tagevents.op_code is '"c"->created, "d"->deleted';

    insert  into schema_changes (version, script)  values (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END
$$;

