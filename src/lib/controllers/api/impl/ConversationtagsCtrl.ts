import { throwUndefinedError } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { Request } from "express";
import { ConversationstagsFilter } from "lib/dao/IConversationsTagsDAO";

import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { PoolClient } from "pg";
import { jsonArrayMember, jsonMember, jsonObject } from "typedjson";
import { ConversationDto, ConversationPKDto, ConversationtagDto, ConversationtagPkDto, DomainPkDto, TagPkDto } from "whatsapp-srv-dtos";
import { IConversationtagsCtrl } from "../IConversationtagsCtrl";
import { ITagsCtrl } from "../ITagsCtrl";


export function ConversationtagsCtrl(
  {
    db: { withTransaction },
    avatarsDAO,
    contactsDAO,
    conversationsDAO,
    tagsDAO,
    conversationtagsDAO: conversationsTagsDAO,
    restUtils: { asRest, withParsedRequest },
    controllerUtils: { withAuthorizedUser }
  }: IContainer
): IConversationtagsCtrl {
  return {
    getConversationtags: asRest({ nullAs404: true }, getConversationtags),
    getConversationtag: asRest({ nullAs404: true }, getConversationtag),
    postConversationtag: asRest({ nullAs404: true }, postConversationtag),
    deleteConversationtag: asRest({ nullAs404: true }, deleteConversationtag),
  };


  function getConversationtag(req: Request) {
    return withParsedRequest(GetConversationtagRequest, ({ params: { avatar_id, contact_id, tag_id } }) =>
      withAuthorizedUser("conversationtag_get",
        async (user) => {
          const [conversationtag] = await conversationsTagsDAO.list({
            conversation_avatar_id: avatar_id,
            conversation_contact_id: contact_id,
            tag_id: tag_id,
            domain_id: user.domain.id,
            detail_tag: true,
          })();
          return conversationtag;
        }
      )(req)
    )(req);
  }

  function deleteConversationtag(req: Request) {
    return withParsedRequest(DelConversationtagRequest, ({ params: { avatar_id, contact_id, tag_id } }) =>
      withAuthorizedUser("conversationtag_delete", async (usr) =>
        withTransaction(async (trx) => {
          const key = {
            conversation: { // TODO: Evaluar el uso de un ID generado en las conversaciones para evitar los problemas del uso de claves compuestas 
              avatar: { id: avatar_id }, 
              contact: { id: contact_id } 
            },
            tag: { id: tag_id }
          } as ConversationtagPkDto; 

          if (await isConversationtagInDomain(key, usr.domain)(trx)) {
            await conversationsTagsDAO.remove(key)(trx);

            return key;
          } else
            return void 0; // Not found 404
        })()
      )(req)
    )(req);
  }

  function getConversationtags(req: Request) {
    return withParsedRequest(GetConversationtagsRequest, ({ query }) =>
      withAuthorizedUser("conversationtag_get",
        async (user) => 
        (await conversationsTagsDAO.list({
          ...query,
          domain_id: user.domain.id,
        })()).map(ct=>ct.tag)
      )(req)
    )(req);
  }

  /**
   * POST /conversations/:avatar_id/:contact_id/tags
   * BODY {
   *   tag:{id:number}
   * }
   */
  function postConversationtag(req: Request) {
    return withParsedRequest(PostConversationTagRequest, ({ body: data }) =>
      withAuthorizedUser("conversationtag_post", async ({domain}) =>
        withTransaction(async (trx) => {

          if (
            await isConversationInDomain(data.conversation, domain)(trx) &&
            await isTagInDomain(data.tag, domain)(trx)
          ) {
            const key = await conversationsTagsDAO.create(data)(trx);
            // En realidad, los datos que se obtendrán será la propia key... pero lo dejamos por si añadimos datos adicionales a conversationstags 
            return conversationsTagsDAO.read(key)(trx);
          } else
            return void 0;

        })()
      )(req)
    )(req);
  }




  function isTagInDomain(key: TagPkDto, domain: DomainPkDto) {
    return async (cli: PoolClient) => 0 !== await tagsDAO.count({ id: key.id, domain_id: domain.id })(cli);
  }
  function isConversationInDomain(key: ConversationPKDto, domain: DomainPkDto) {
    return async (cli: PoolClient) => 0 !== await conversationsDAO.count({
      avatar_id: key.avatar.id,
      contact_id: key.contact.id,
      domain_id: domain.id
    })(cli);
  }
  function isConversationtagInDomain(key: ConversationtagPkDto, domain: DomainPkDto) {
    return async (cli: PoolClient) => 0 !== await conversationsTagsDAO.count({
      conversation_avatar_id: key.conversation.avatar.id,
      conversation_contact_id: key.conversation.contact.id,
      tag_id: key.tag.id,
      domain_id: domain.id
    })(cli);
  }
}

//#region estructuras de datos con las que parseamos params/query/body





/**
 * 1 Conversación 1 Tag
 * /conversations/:avatar_id/:contact_id/tags/:tag_id
 */
@jsonObject
class GetConversationtagParams {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  avatar_id!: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  contact_id!: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  tag_id!: number
}
@jsonObject
class GetConversationtagRequest {
  @jsonMember(GetConversationtagParams, { isRequired: true })
  params!: GetConversationtagParams
}
@jsonObject
class DelConversationtagRequest {
  @jsonMember(GetConversationtagParams, { isRequired: true })
  params!: GetConversationtagParams
}
/**
 * 
 * /conversations?<query>
 */
@jsonObject
class GetConversationtagsQuery implements ConversationstagsFilter {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: false })
  avatar_id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: false })
  contact_id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: false})
  tag_id?: number
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer(), isRequired: false })
  detail_tag?:boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer(), isRequired: false })
  detail_conversation_avatar?:boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer(), isRequired: false })
  detail_conversation_contact?:boolean
}
@jsonObject
class GetConversationtagsRequest {
  @jsonMember(GetConversationtagsQuery, { isRequired: true })
  query!: GetConversationtagsQuery
}

/**
 * POST /conversationtags
 * BODY ConversationtagPkDto
 *  
 */

@jsonObject
class IdOnly {
  @jsonMember(Number, { isRequired: true })
  id!: number  
}

@jsonObject
class ConversationPK implements ConversationPKDto {
  @jsonMember(IdOnly, { isRequired: true })
  avatar!: IdOnly
  @jsonMember(IdOnly, { isRequired: true })
  contact!: IdOnly
}

@jsonObject
class ConversationtagPk implements ConversationtagPkDto{
  @jsonMember(ConversationPK, { isRequired: true })
  conversation!:ConversationPK
  @jsonMember(IdOnly, { isRequired: true })
  tag!: IdOnly  
}
@jsonObject
class PostConversationTagRequest {
  @jsonMember(ConversationtagPk, { isRequired: true })
  body!: ConversationtagPk
}





///#region
