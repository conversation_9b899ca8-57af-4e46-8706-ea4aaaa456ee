-- Los campos de conversations_tags que referencian aia conversations debe incluir el prefijo "conversations_"  (por convención)
DO $$
DECLARE
  new_version integer:= 10;
BEGIN
  IF (select max(version) from schema_changes) = (new_version - 1) THEN

    
    drop table conversations_tags;
    create table conversations_tags (
      conversation_avatar_id integer NOT NULL,
      conversation_contact_id integer not null,
      tag_id integer NOT NULL,
      primary key (conversation_avatar_id, conversation_contact_id, tag_id),
      foreign key (conversation_avatar_id, conversation_contact_id) references conversations (avatar_id, contact_id ) on delete cascade on update restrict,
      foreign key (tag_id) references tags(id) on delete cascade on update restrict
    );
    comment on table conversations_tags is '
      Cada uno de los tags asignados a una conversación
    ';
    
    drop table conversations_tagevents;
    create table conversations_tagevents (
      id bigserial NOT NULL,
      conversation_avatar_id integer NOT NULL,
      conversation_contact_id integer NOT NULL,
      tag_id integer NOT NULL,
      at timestamptz NOT NULL DEFAULT now( ),
      op_code character varying NOT NULL CHECK (op_code = 'c' OR op_code = 'd' ),
      --primary key (avatar_id, contact_number, tag_id, timestamp)
      primary key (id ),
      foreign key (conversation_avatar_id, conversation_contact_id ) references conversations(avatar_id, contact_id ) on delete cascade on update restrict,
      foreign key (tag_id) references tags (id) on delete cascade on update restrict
    );
    comment on table conversations_tagevents is 'Cada evento de añadir/quitar un tag a una conversación';
    comment on column conversations_tagevents.op_code is '"c"->created, "d"->deleted';

    insert  into schema_changes (version, script)  values (new_version, to_char(new_version, 'FM000') || '.sql');
  END IF;
END
$$;

