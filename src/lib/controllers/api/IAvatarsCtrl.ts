import { NextFunction, Request, Response } from "express";

export interface IAvatarsCtrl {
  //createAccount(req: Request, res: Response, next: NextFunction): void
  getAvatar(request: Request, response: Response, next: NextFunction): void
  getAvatars(request: Request, response: Response, next: NextFunction): void
  putAvatar(request: Request, response: Response, next: NextFunction): void
  postAvatar(request: Request, response: Response, next: NextFunction): void
  deleteAvatar(request: Request, response: Response, next: NextFunction): void
}