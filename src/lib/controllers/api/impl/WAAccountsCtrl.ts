import { Db } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { NextFunction, Request, Response } from "express";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { jsonMember, jsonObject } from "typedjson";
import { IWAAccountsCtrl } from "../IWAAccountsCtrl";



export function WAAccountsCtrl(
  {
    restUtils: { asRest, asJpeg, asValidRequest, withParsedRequest },
    controllerUtils: { withAuthorizedUser },
    waclientsBusiness: messagesHub,
    waaccountsDAO,
    db,
  }: IContainer
): IWAAccountsCtrl {

  return {
    putAccount: asRest({ nullAs404: true }, asValidRequest("/waaccounts/put_account", putAccount)),
    getAccount: asRest({ nullAs404: true }, asValidRequest("/waaccounts/get_account", getAccount)),
    getAccounts: asRest({ nullAs404: true }, asValidRequest("/waaccounts/get_accounts", getAccounts)),
    getClientStatus: asRest({ nullAs404: true }, asValidRequest("/waaccounts/get_account", getClientStatus)),
    getClientSnapshotAsJpeg: asJpeg({ nullAs404: true }, asValidRequest("/waaccounts/get_account", getClientSnapshotAsJpeg)),
  };


  function getAccount(req: Request) {
    return withParsedRequest(GetWaAccountRequest, ({ params: { waaccount_id } }) =>
      withAuthorizedUser("account_get", async (user) => {
        const [account] = await waaccountsDAO.list({ id: waaccount_id, avatar_domain_id: user.domain.id })();
        return account;
      })(req)
    )(req);
  }
  function getAccounts(req: Request) {
    return withAuthorizedUser("account_get", async (user) =>
      waaccountsDAO.list({ avatar_domain_id: user.domain.id })()
    )(req);
  }
  function putAccount(req: Request) {
    const pk = { id: req.params["waaccount_id"] as string };
    const data = req.body;
    return withAuthorizedUser("account_put", async (_) =>
      db.withTransaction(async (trx) => {
        const cnt = await waaccountsDAO.update(pk, data)(trx);
        if (cnt !== 0)
          return await waaccountsDAO.read(pk)(trx);
        else
          return null;
      })()
    )(req);
  }
  function getClientStatus(req: Request) {
    const waAccountId = req.params["waaccount_id"] as string;
    return withAuthorizedUser("account_get", async ({ domain }) => {
      if (await isInDomain(waAccountId, domain.id)) {
        return messagesHub.getClientStatus(waAccountId);
      } else {
        return void 0;
      }
    })(req);
  }

  function getClientSnapshotAsJpeg(req: Request) {

    return withParsedRequest(GetClientSnapshotAsJpeg, ({ params: { waaccount_id } }) =>
      withAuthorizedUser("account_get", async ({ domain }) => {
        if (await isInDomain(waaccount_id, domain.id))
          return messagesHub.getClientSnapshotAsJpeg(waaccount_id);
        else
          return void 0;
      })(req)
    )(req);
  }

  async function isInDomain(waaccount_id: string, domain_id: number) {
    return (await waaccountsDAO.list({ id: waaccount_id, avatar_domain_id: domain_id })()).length !== 0;
  }

}


//#region estructuras de datos con las que parseamos params/query/body
@jsonObject
class GetWaAccountParams {
  @jsonMember(String, { isRequired: true })
  waaccount_id!: string
}
@jsonObject
class GetWaAccountRequest {
  @jsonMember(GetWaAccountParams, { isRequired: true })
  params!: GetWaAccountParams
}
@jsonObject
class GetClientSnapshotAsJpeg {
  @jsonMember(GetWaAccountParams, { isRequired: true })
  params!: GetWaAccountParams
}
//#endregion