import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { IContainer_db } from "container/IContainer";
import { AvatarDto, AvatarPkDto } from "whatsapp-srv-dtos";
import { AvatarsFilter, IAvatarsDAO } from "../IAvatarsDAO";
import { row2DTO } from "./row2dto";
import { AvatarsFields, AvatarsTable } from "./schema/tables/avatarsTable";
const { insertRecord, updateRecord, removeRecord } = DbCrudUtils;
const { buildSqlFields, buildSqlFrom } = SqlHelpers;
const { addEq } = SqlWhereHelpers;

export function AvatarsDAO({ db, dbSchema }: IContainer_db): IAvatarsDAO {
  const flds = AvatarsFields;
  const alias = "avt";

  return {
    list,
    count,
    create,
    read,
    update,
    remove
    //readByApikey,
  };


  function list(filter: AvatarsFilter = {}) {
    const {values,terms} = buildConditions(filter);

    const qryOptions = {
      table: AvatarsTable,
      tableAlias: alias,
    };
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryOptions);

    const qry = `select ${qryFields} from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli => {
      const rows = await dbCli.query(qry, values).then(result => result.rows);
      return rows.map(row => row2DTO.toAvatar(row, alias))
    });
  }
  function count(filter: AvatarsFilter = {}) {
    const {values,terms} = buildConditions(filter);
    
    const qryOptions = {
      table: AvatarsTable,
      tableAlias: alias,
    };
    const
      qryFrom = buildSqlFrom(dbSchema, qryOptions);

    const qry = `select count(*) as "howMany" from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli =>
      dbCli.query(qry, values).then(
        result => parseInt(result.rows[0]["howMany"])
      )
    );
  }
  function buildConditions(filter: AvatarsFilter={}){
    var values: any[] = [];
    var terms: string[] = ["true"];
    addEq(terms, values, `${alias}.${flds.domain_id}`, filter.domain_id);
    addEq(terms, values, `${alias}.${flds.id}`, filter.id);

    return {values, terms}
  }
  function read({id}: AvatarPkDto) {
    return db.withClient(async cli => {
      const [avatar] = await list({ id })(cli);
      return avatar;
    });
  }
  function update({ id }: AvatarPkDto, data: AvatarDto) {
    console.log({ id, data });
    return db.withTransaction(updateRecord(AvatarsTable, { ...data, id }));
  }
  function create(avatar: AvatarDto) {
    return db.withTransaction(async trx => {
      const rw = await insertRecord(AvatarsTable, avatar)(trx);
      return row2DTO.toAvatarPK(rw);
    });
  }
  function remove({ id }: AvatarPkDto) {
    return db.withTransaction(removeRecord(AvatarsTable, { id }));
  }
}

