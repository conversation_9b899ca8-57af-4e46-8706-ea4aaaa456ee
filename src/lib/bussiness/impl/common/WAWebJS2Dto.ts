import { WAContactDto, WAMessageDto } from "whatsapp-srv-dtos";
import WAWebJS from "whatsapp-web.js";

export namespace WAWebJS2Dto {
  /**
  * Convierte un contacto proveído por WAWebJS a nuestro formato propio de contacto
  * @param account_id 
  * @param contact WAWebJS.contact Contacto proveído por WAWebJS.
  * @returns 
  */
  export function toWAContactDto(account_id: string, contact: WAWebJS.Contact): WAContactDto {
    return {
      account: { id: account_id },
      id: contact.id.user,
      isMe: contact.isMe,
      name: contact.name ?? contact.shortName ?? contact.pushname,
      meta: JSON.parse(JSON.stringify(contact))
    };
  }

  /**
   * Convierte un mensaje proveído por WAWebJS a nuestro formato propio de mensaje
   * @param account_id 
   * @param message Mensaje proveído por WAWebJS
   * @returns 
   */
  export function toWAMessageDto(account_id: string, message: WAWebJS.Message): WAMessageDto {
    return {
      account: { id: account_id },
      id: message.id.id,
      from: {
        account: { id: account_id },
        id: removeServertokenFromId(message.from),
      },
      to: {
        account: { id: account_id },
        id: removeServertokenFromId(message.to),
      },
      body: message.body,
      type: message.type,
      stamp: new Date(message.timestamp * 1000),
      original: message.rawData,
    }
  }

  function removeServertokenFromId(id: string) {
    const idx = id.indexOf("@");
    return (idx !== -1) ? id.substring(0, idx) : id;
  }
}

