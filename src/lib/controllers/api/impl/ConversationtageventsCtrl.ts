import { IContainer } from "container/IContainer";
import { Request } from "express";
import { ConversationstageventsFilter } from "lib/dao/IConversationsTageventsDAO";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { jsonMember, jsonObject } from "typedjson";
import { ConversationPKDto, ConversationtagPkDto } from "whatsapp-srv-dtos";
import { IConversationtageventsCtrl } from "../IConversationtageventsCtrl";

export function ConversationtageventsCtrl(
  {
    conversationtageventsDAO,
    restUtils: { asRest, withParsedRequest },
    controllerUtils: { withAuthorizedUser }
  }: IContainer
): IConversationtageventsCtrl {
  return {
    getConversationtagevents: asRest({ nullAs404: true }, getConversationtagevents),
  };

  function getConversationtagevents(req: Request) {
    return withParsedRequest(GetConversationtageventsRequest, ({ query }) =>
      withAuthorizedUser("conversationtag_get",
        async (user) =>
          await conversationtageventsDAO.list({
            ...query,
            domain_id: user.domain.id,
          })()
      )(req)
    )(req);
  }


}

//#region estructuras de datos con las que parseamos params/query/body





/**
 * 1 Conversación 1 Tag
 * /conversations/:avatar_id/:contact_id/tags/:tag_id
 */
@jsonObject
class GetConversationtagParams {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  avatar_id!: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  contact_id!: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: true })
  tag_id!: number
}
@jsonObject
class GetConversationtagRequest {
  @jsonMember(GetConversationtagParams, { isRequired: true })
  params!: GetConversationtagParams
}
@jsonObject
class DelConversationtagRequest {
  @jsonMember(GetConversationtagParams, { isRequired: true })
  params!: GetConversationtagParams
}
/**
 * 
 * /conversations?<query>
 */
@jsonObject
class GetConversationtageventsQuery implements ConversationstageventsFilter {
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: false })
  avatar_id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: false })
  contact_id?: number
  @jsonMember(Number, { deserializer: TypedJsonUtils.strAsIntDeserializer(), isRequired: false })
  tag_id?: number
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer(), isRequired: false })
  detail_tag?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer(), isRequired: false })
  detail_conversation_avatar?: boolean
  @jsonMember(Boolean, { deserializer: TypedJsonUtils.strAsBoolDeserializer(), isRequired: false })
  detail_conversation_contact?: boolean
}
@jsonObject
class GetConversationtageventsRequest {
  @jsonMember(GetConversationtageventsQuery, { isRequired: true })
  query!: GetConversationtageventsQuery
}

/**
 * POST /conversationtags
 * BODY ConversationtagPkDto
 *  
 */

@jsonObject
class IdOnly {
  @jsonMember(Number, { isRequired: true })
  id!: number
}

@jsonObject
class ConversationPK implements ConversationPKDto {
  @jsonMember(IdOnly, { isRequired: true })
  avatar!: IdOnly
  @jsonMember(IdOnly, { isRequired: true })
  contact!: IdOnly
}

@jsonObject
class ConversationtagPk implements ConversationtagPkDto {
  @jsonMember(ConversationPK, { isRequired: true })
  conversation!: ConversationPK
  @jsonMember(IdOnly, { isRequired: true })
  tag!: IdOnly
}
@jsonObject
class PostConversationTagRequest {
  @jsonMember(ConversationtagPk, { isRequired: true })
  body!: ConversationtagPk
}





///#region
