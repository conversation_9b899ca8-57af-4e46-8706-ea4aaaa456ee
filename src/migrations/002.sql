DO $$
DECLARE
new_version integer:=2;
BEGIN

  IF (select max(version) from schema_changes)=(new_version-1) THEN
    /* BEGIN */
  
    /** Generates a unique global key */
    CREATE OR REPLACE FUNCTION newkey()
        RETURNS character varying
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE 
    AS $BODY$
    BEGIN
      RETURN left(encode(decode(md5(concat(random()::character varying, now()::character varying)),'hex'),'base64'), 22);
    END;
    $BODY$;

    CREATE TABLE users
    (
      id character varying  NOT NULL DEFAULT newkey(),
      name character varying ,
      CONSTRAINT users_pkey PRIMARY KEY (id)
    );
    GRANT ALL ON TABLE users TO app_user;

    CREATE TABLE apikeys
    (
      key character varying  NOT NULL,
      user_id character varying  NOT NULL,
      CONSTRAINT apikeys_pkey PRIMARY KEY (key),
      CONSTRAINT apikeys_user_id_fkey FOREIGN KEY (user_id)
        REFERENCES users (id) 
        ON UPDATE RESTRICT
        ON DELETE CASCADE
    );
    GRANT ALL ON TABLE apikeys TO app_user;

    CREATE TABLE waaccounts
    (
      id character varying  NOT NULL,
      notificationemail character varying  NOT NULL,
      CONSTRAINT accounts_pkey PRIMARY KEY (id)
    );
    GRANT ALL ON TABLE waaccounts TO app_user;

    CREATE TABLE wacontacts
    (
        account_id character varying NOT NULL,
        id character varying NOT NULL,
        meta json NOT NULL,
        "isMe" boolean NOT NULL,
        CONSTRAINT wacontacts_pkey PRIMARY KEY (account_id, id),
        CONSTRAINT wacontacts_account_id_fkey FOREIGN KEY (account_id)
            REFERENCES waaccounts (id) 
            ON UPDATE RESTRICT
            ON DELETE CASCADE
    );
    GRANT ALL ON TABLE wacontacts TO app_user;
    
    COMMENT ON TABLE wacontacts
        IS 'Un contacto de whatsapp.';
    
    COMMENT ON COLUMN wacontacts.id
        IS 'Identificador del contacto en whatsapp.  Se usa su número de teléfono si es un particular';
    
    COMMENT ON COLUMN wacontacts."isMe"
        IS 'El contacto soy yo (desde el punto de vista de la cuenta)';


    CREATE TABLE wamessages
    (
        account_id character varying  NOT NULL,
        id character varying  NOT NULL,
        from_id character varying  NOT NULL,
        to_id character varying  NOT NULL,
        stamp timestamp with time zone NOT NULL,
        body character varying  NOT NULL,
        type character varying  NOT NULL,
        original json NOT NULL,
        insertionindex bigserial NOT NULL,
        CONSTRAINT wamessages_pkey PRIMARY KEY (account_id, id),
        CONSTRAINT wamessages_account_id_from_id_fkey FOREIGN KEY (from_id, account_id)
            REFERENCES wacontacts (id, account_id)
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        CONSTRAINT wamessages_account_id_to_id_fkey FOREIGN KEY (to_id, account_id)
            REFERENCES wacontacts (id, account_id)
            ON UPDATE RESTRICT
            ON DELETE CASCADE
    );
       
    
    GRANT ALL ON TABLE wamessages TO app_user;
    
    COMMENT ON TABLE wamessages
        IS 'Todos los mensajes de los chats.
    Es obligatorio indicar from_id (contacto origen)';
    
    COMMENT ON COLUMN wamessages.id
        IS 'Identificador asignado por whatsapp al mensaje.  Es único por cuenta.
    Si hay comunicación entre cuentas, el mismo mensaje aparecerá en diferentes cuentas (estará repetido)';
    
    COMMENT ON COLUMN wamessages.from_id
        IS 'Identificador de la cuenta que envía el mensaje (Identificador whatsapp.  Número de teléfono si es un particular)';
    
    COMMENT ON COLUMN wamessages.to_id
        IS 'Identificador de la cuenta que recibe el mensaje (Identificador whatsapp.  Número de teléfono si es un particular)
    ';
    
    COMMENT ON COLUMN wamessages.stamp
        IS 'Stamp (precisión de segundos) del mensaje.  Varios mensajes pueden tgener el mismo stamp.';
    
    COMMENT ON COLUMN wamessages.body
        IS 'Texto del mensaje.  Cuando el mensaje es de tipo diferente a "chat" este suele estar vacío ("")';
    
    COMMENT ON COLUMN wamessages.type
        IS 'chat|document|video|...   tipo de contenido.
    ';
    
    COMMENT ON COLUMN wamessages.original
        IS 'Mensaje original (JSON)
    ';
    
    COMMENT ON COLUMN wamessages.insertionindex
        IS 'Autonumérico que indica el orden de inserción en la BBDD.  Puede ser usado como complemento a "stamp" para asegurar un orden en las consultas.
    1ºOJO...  no poodemos asegurar que el orden en el que se han insertado en la BBDD coincide con el orden de envío/recepción (la inserción puede producirse por sincronización o por evento de recepción/envío)';



    CREATE TABLE bat_tasks
    (
        id bigserial NOT NULL,
        scheduling_start timestamp with time zone NOT NULL DEFAULT now(),
        scheduling_deadline timestamp with time zone,
        finished_at timestamp with time zone,
        created_at timestamp with time zone NOT NULL DEFAULT now(),
        CONSTRAINT batches_pkey PRIMARY KEY (id)
    );
    
    GRANT ALL ON TABLE bat_tasks TO app_user;
    
    COMMENT ON COLUMN bat_tasks.scheduling_start
        IS 'Intante en el que debe iniciarse el proceso batch';
    
    COMMENT ON COLUMN bat_tasks.scheduling_deadline
        IS 'Instante en el que el batch ha finalizado su ejecución';
    
    COMMENT ON COLUMN bat_tasks.finished_at
        IS 'Fecha máxima para finalizar el proceso batch (Ej: si hay mensajes pendientes de enviar, no se enivarán pasada esa fecha)';
    
    COMMENT ON COLUMN bat_tasks.created_at
        IS 'Cuando se ha creado el batch';

    CREATE TABLE public.bat_messages
    (
        task_id bigint NOT NULL,
        id integer NOT NULL,
        from_number character varying NOT NULL,
        to_number character varying NOT NULL,
        content_body character varying NOT NULL,
        content_type character varying NOT NULL DEFAULT 'chat'::character varying,
        status_code character varying DEFAULT 'pending'::character varying,
        sentmessage_account_id character varying,
        sentmessage_id character varying,
        error_code character varying,
        error_text character varying,
        CONSTRAINT bat_messages_pk PRIMARY KEY (task_id, id),
        CONSTRAINT bat_messages_sentmessage_fk FOREIGN KEY (sentmessage_id, sentmessage_account_id)
            REFERENCES public.wamessages (id, account_id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE RESTRICT,
        CONSTRAINT bat_messages_task_id_fk FOREIGN KEY (task_id)
            REFERENCES public.bat_tasks (id) MATCH SIMPLE
            ON UPDATE RESTRICT
            ON DELETE CASCADE,
        CONSTRAINT bat_taskmessages_content_type_code_check CHECK (content_type::text = 'chat'::text),
        CONSTRAINT bat_taskmessages_status_code_check CHECK (status_code::text = 'pending'::text OR status_code::text = 'sent'::text OR status_code::text = 'error'::text)
    );
    GRANT ALL ON TABLE public.bat_messages TO app_user;
  
    COMMENT ON COLUMN public.bat_messages.task_id
        IS 'tarea de lotes a la que pertenece el mensaje';
    /* END */
    INSERT INTO schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000') || '.sql');
  END IF;

END$$;