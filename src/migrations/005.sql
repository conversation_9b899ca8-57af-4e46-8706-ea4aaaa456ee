DO $$
DECLARE
new_version integer:=5;
BEGIN

  IF (select max(version) from schema_changes)=(new_version-1) THEN
    /* BEGIN */
  
	alter table wacontacts
      add column "name" character varying;

    update wacontacts set name = coalesce(meta->>'name'::varchar, meta->>'shortName'::varchar, meta->>'pushname'::varchar) where name is null;
    
    /* END */
    INSERT INTO schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000') || '.sql');
  END IF;

END$$;
