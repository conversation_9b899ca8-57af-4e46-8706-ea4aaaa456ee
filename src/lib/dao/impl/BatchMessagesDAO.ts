import { DbCrudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { IContainer_db } from "container/IContainer";
import { BatchMessageDto, BatchMessageErrorCode, BatchMessageKeyStruct, BatchMessageStatusCode } from "whatsapp-srv-dtos";
import { BatchTaskKeyStruct } from "whatsapp-srv-dtos";
import { NonEmptyArr } from "lib/utils/CommonTypes";
import { BatchMessagesFilter, IBatchMessagesDAO } from "../IBatchMessagesDAO";
import { row2DTO } from "./row2dto";
import { batchMessagesFields, batchMessagesTable } from "./schema/tables/batchMessagesTable";

const { addEq } = SqlWhereHelpers;
const { buildSqlFields, buildSqlFrom } = SqlHelpers;
const { insertRecord, updateRecord } = DbCrudUtils;

export function BatchMessagesDAO({ db, dbSchema }: IContainer_db): IBatchMessagesDAO {
  const flds = batchMessagesFields
  const alias = "batchmessage"

  return {
    list,
    read,
    create,
    update,
    set_pending_to_deadline_exceeded_by_task,
    inc_triescount,
  }


  function list(filter: BatchMessagesFilter = {}) {
    const
      { values, terms, order_by, offset, limit } = conditions(filter);
    const
      qryOptions = { table: batchMessagesTable, tableAlias: alias }
    const
      qryFields = buildSqlFields(dbSchema, qryOptions).join(', '),
      qryFrom = buildSqlFrom(dbSchema, qryOptions),
      qryOrderBy = order_by.join(", ")
    const
      qry = `select ${qryFields} from ${qryFrom} where ${terms.join(" and ")} order by ${qryOrderBy} offset ${offset} limit ${limit}`

    return db.withClient(async dbCli => {
      const qryResult = await dbCli.query(qry, values);
      return qryResult.rows.map(row => row2DTO.toBatchMessageStruct(row, alias));
    });
  }
  function read(pk: BatchMessageKeyStruct) {
    return db.withClient(async cli => {
      const [batchMessage] = await list({ task_id: pk.task.id, id: pk.id })(cli)
      return batchMessage
    });
  }
  function create(data: BatchMessageDto) {
    return db.withTransaction(async trx => {
      const rw = await insertRecord(batchMessagesTable, data)(trx);
      return row2DTO.toBatchMessagePk(rw)
    })
  }
  function update(data: BatchMessageDto) {
    return db.withTransaction(updateRecord(batchMessagesTable, data))
  }
  /**
   * Cancela todos los mensajes batch de una tarea que aún están pendientes.
   * La causa será "deadline_exceeded"
   * @param taskPK 
   * @returns 
   */
  function set_pending_to_deadline_exceeded_by_task(taskPK: BatchTaskKeyStruct) {
    const qry = [
      `update`,
      `  ${batchMessagesTable.name}`,
      `set`,
      `  ${batchMessagesFields.status_code}=$${1},`,
      `  ${batchMessagesFields.error_code}=$${2}`,
      `where`,
      `  ${batchMessagesFields.task_id}=$${3}`,
      `  and ${batchMessagesFields.status_code}=$${4}`
    ].join(" ");
    const params = [
      BatchMessageStatusCode.error,
      BatchMessageErrorCode.deadline_exceeded,
      taskPK.id,
      BatchMessageStatusCode.pending,
    ];
    return db.withTransaction(trx =>
      trx.query(qry, params).then(result => result.rowCount??0)
    );
  }
  /**
   * Incrementa el valor triescount del mensaje batch
   * @param pk 
   * @returns 
   */
  function inc_triescount(pk: BatchMessageKeyStruct) {
    const qry = [
      `update`,
      `  ${batchMessagesTable.name}`,
      `set`,
      `  ${batchMessagesFields.triescount}=${batchMessagesFields.triescount}+1`,
      `where`,
      `  ${batchMessagesFields.task_id}=$1`,
      `  and ${batchMessagesFields.id}=$2`
    ].join("\n");
    const params = [
      pk.task.id,
      pk.id
    ];

    return db.withTransaction(trx =>
      trx.query(qry, params).then(result => result.rowCount??0)
    );
  }

  function conditions(filter: BatchMessagesFilter = {}) {
    let values: any[] = []
    let terms: NonEmptyArr<string> = ["true"]
    addEq(terms, values, `${alias}.${flds.id}`, filter.id)
    addEq(terms, values, `${alias}.${flds.task_id}`, filter.task_id)
    addEq(terms, values, `${alias}.${flds.from_number}`, filter.from_number)
    addEq(terms, values, `${alias}.${flds.to_number}`, filter.to_number)
    addEq(terms, values, `${alias}.${flds.status_code}`, filter.status_code)

    const limit: number = filter.pagination?.limit ?? 1000;
    const offset: number = filter.pagination?.offset ?? 0;
    const order_by: NonEmptyArr<string> = (sortby => {
      switch (sortby) {
        case "task_id_asc_id_asc": return [`${alias}.${flds.task_id} asc`, `${alias}.${flds.id} asc`];
        case "task_id_desc_id_desc": return [`${alias}.${flds.task_id} desc`, `${alias}.${flds.id} desc`];
        case "id_asc": return [`${alias}.${flds.id} asc`];
        case "id_desc": return [`${alias}.${flds.id} desc`];
        case "triescount_asc_id_asc": return [`${alias}.${flds.triescount} asc`, `${alias}.${flds.id} asc`];
        case "task_id_asc_triescount_asc_id_asc": return [`${alias}.${flds.task_id} asc`, `${alias}.${flds.triescount} asc`, `${alias}.${flds.id} asc`];
        // Orden por defecto
        case void 0: return [`${alias}.${flds.task_id} asc`, `${alias}.${flds.id} asc`];
        // Desconocido
        default: throw new Error(`Unknown sortby ${sortby}`);
      }
    })(filter.sortby)

    return { values, terms, order_by, limit, offset }
  }

}

