//import { appRoutes } from "./app";
import { throwUndefinedError } from 'agentor-lib';
import compression from 'compression';
import { AppContainer } from "container/AppContainer";
import cors from 'cors';
import express from 'express';
// Es posible que puppeter esté necesitando más de la cuenta... (whatsappweb.js)
require('events').EventEmitter.defaultMaxListeners = 100; 

const config = require("../config/app.js");

const app = express();
const PORT = config.http.port;
// USADO en la generación de tokens (JWT).
// Debe provenir de la configuración
if (!process.env.ACCESS_TOKEN_SECRET) {
  process.env.ACCESS_TOKEN_SECRET = config.jwt?.ACCESS_TOKEN_SECRET ?? throwUndefinedError("Falta config.jwt.ACCESS_TOKEN_SECRET o env.ACCESS_TOKEN_SECRET");
}

// USADO en la generación de tokens (JWT).
// Debe provenir de la configuración
//if (!env.ACCESS_TOKEN_SECRET) {
//  env.ACCESS_TOKEN_SECRET = config.jwt.ACCESS_TOKEN_SECRET;
//}

AppContainer(config).withContainer(async ({ 
  logger, 
  hooksSrv,
  waclientsBusiness, 
  batchTasksRunner, 
  batchsendsRunner, 
  authCtrl, 
  avatarsCtrl, 
  tagsCtrl,
  conversationtagsCtrl, 
  conversationtageventsCtrl,
  batchsendsCtrl,
  waAccountsCtrl, 
  waContactsCtrl, 
  waMessagesCtrl, 
  batchTasksCtrl, 
  waclients,
}) => {
  try {
    await waclientsBusiness.bootstrap();
    await batchsendsRunner.bootstrap();
    await batchTasksRunner.bootstrap();
    process.on("SIGTERM", async (signals) => {
      logger.info(`SIGTERM detected`);
      try { await batchsendsRunner.dispose(); } catch (e) { logger.error(e); }
      try { await batchTasksRunner.dispose(); } catch (e) { logger.error(e); }
      try { await waclientsBusiness.dispose(); } catch (e) { logger.error(e); }
      try { await waclients.dispose(); } catch (e) { logger.error(e); }
      try { await hooksSrv.dispose(); } catch (e) { logger.error(e); }
    });
    app.
      enable("trust proxy").
      use(compression()).
      use(cors({
        "origin": "*",
        "methods": "GET,HEAD,PUT,PATCH,POST,DELETE",
        "preflightContinue": true,
        "optionsSuccessStatus": 204
      })).
      //use(bodyParser.json({ strict: true })).
      use(express.json()).
      use(express.urlencoded({ extended: true })).
      use(express.static("public")).
      post("/oauth/tokens", authCtrl.createToken).
      get("/avatars", avatarsCtrl.getAvatars).
      post("/avatars", avatarsCtrl.postAvatar).
      get("/avatars/:avatar_id", avatarsCtrl.getAvatar).
      put("/avatars/:avatar_id", avatarsCtrl.putAvatar).
      delete("/avatars/:avatar_id", avatarsCtrl.deleteAvatar). 
      get("/tags", tagsCtrl.getTags).
      post("/tags", tagsCtrl.postTag).
      get("/tags/:tag_id", tagsCtrl.getTag).
      put("/tags/:tag_id", tagsCtrl.putTag).
      delete("/tags/:tag_id", tagsCtrl.deleteTag).
      post("/conversations/:avatar_id/:contact_id/messages", waMessagesCtrl.postConversationMessage).
      post("/conversationtags", conversationtagsCtrl.postConversationtag).
      get("/conversationtags", conversationtagsCtrl.getConversationtags).
      get("/conversationtagevents", conversationtageventsCtrl.getConversationtagevents).
      get("/conversationtags/:avatar_id/:contact_id/:tag_id", conversationtagsCtrl.getConversationtag).
      delete("/conversationtags/:avatar_id/:contact_id/:tag_id", conversationtagsCtrl.deleteConversationtag).
      get("/waaccounts", waAccountsCtrl.getAccounts).
      post("/batchsends", batchsendsCtrl.postBatchsend).
      get("/waaccounts/:waaccount_id", waAccountsCtrl.getAccount).
      put("/waaccounts/:waaccount_id", waAccountsCtrl.putAccount).
      get("/waaccounts/:waaccount_id/waclient/status", waAccountsCtrl.getClientStatus).
      get("/waaccounts/:waaccount_id/waclient/snapshot.jpg", waAccountsCtrl.getClientSnapshotAsJpeg).
      get("/waaccounts/:waaccount_id/wacontacts", waContactsCtrl.getAccountContacts).
      get("/waaccounts/:waaccount_id/wacontacts/:wacontact_id", waContactsCtrl.getAccountContact).
      get("/wamessages", waMessagesCtrl.getMessages).
      post("/wamessages", waMessagesCtrl.postMessage).
      get("/wacontacts", waContactsCtrl.getContacts).
      post("/batchtasks", batchTasksCtrl.postTask).
      listen(PORT, () => {
        logger.info(`⚡️[server]: Server is running at http://localhost:${PORT}`);
      });

  } catch (e) {
    logger.error("Unexpected error", e);
    console.error(e);
  }
});







