import * as pg from "pg";
import { ConversationtagDto, ConversationtagPkDto } from "whatsapp-srv-dtos";

export type ConversationstagsFilter = {
  /** Identificador del avatar */
  conversation_avatar_id?: number
  /** identificador del contacto */
  conversation_contact_id?: number
  /** Identificador del dominio */
  tag_id?: number
  /** 
   * El conversationtag debe pertenecer al dominio indicado.
   *  Por definición, El tag, el avatar y el contacto de un conversationtag pertenecen a ese mismo dominio.  Por eso 
   *  no hay un campo "doamin" en la entidad conversation (Internamente, buscamos conversaciones cuyo avatar sea del dominio)
   */
  domain_id?: number,
  /** Detallar datos adicionales del tag (además de su id) */
  detail_tag?: boolean,
  /** Detallar datos adicionales del contaco (además de su id) */
  detail_conversation_contact?: boolean,
  /** Detallar datos adicionales del avatar (además de su id) */
  detail_conversation_avatar?:boolean,
}

export interface IConversationstagsDAO {
  list(filter?: ConversationstagsFilter): (dbCli?: pg.PoolClient) => Promise<ConversationtagDto[]>;
  count(filter?: ConversationstagsFilter): (dbCli?: pg.PoolClient) => Promise<number>;
  create(data: ConversationtagDto): (dbTrx?: pg.PoolClient) => Promise<ConversationtagPkDto>;
  update(pk: ConversationtagPkDto, data: ConversationtagDto): (dbTrx?: pg.PoolClient) => Promise<number | undefined>;
  read(pk: ConversationtagPkDto): (dbCli?: pg.PoolClient) => Promise<ConversationtagDto | undefined>;
  remove(pk: ConversationtagPkDto): (dbTrx?: pg.PoolClient) => Promise<number | undefined>;
}