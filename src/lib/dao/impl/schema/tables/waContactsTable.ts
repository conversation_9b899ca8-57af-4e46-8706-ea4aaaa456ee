import { DBSchema } from "agentor-lib"

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum waContactsFields {
  account_id = "account_id",
  id = "id",
  /** Representa el contacto a la cuenta de whatsapp? */
  isMe = "isMe",
  name = "name",
  meta = "meta",
  //lastmessage_id="lastmessage_id"

}
const fldN = waContactsFields;

export const waContactsTable = new DBSchema.TableSchema("wacontacts", [
  new DBSchema.TableFieldSchema(fldN.account_id, { c, r, pk, nn }),
  new DBSchema.TableFieldSchema(fldN.id, { c, r, pk, nn }),
  new DBSchema.TableFieldSchema(fldN.isMe, { c, r, nn }),
  new DBSchema.TableFieldSchema(fldN.name, { c, u, r }),
  new DBSchema.TableFieldSchema(fldN.meta, { c, r, u, nn }),
], [
  new DBSchema.TableFKSchema("account", "waaccounts")
]);