import {  MessageDirection, WAMessageDto }  from "whatsapp-srv-dtos";
import * as pg from "pg";


export interface IWAMessagesDAO {
  listIds(filter: IWAMessagesDAO.WAMessagesFilter): (dbCli?: pg.PoolClient) => Promise<string[]>;
  /**
   * Obtiene la versión completa de los mensajes
   */
  list(filter: IWAMessagesDAO.WAMessagesFilter): (dbCli?: pg.PoolClient) => Promise<WAMessageDto[]>;
  /**
   * Obtiene la versión "basica" de los mensajes (sin los datos originales "en bruto" de whatsapp)
   * @param filter 
   */
  listBasic(filter: IWAMessagesDAO.WAMessagesFilter): (dbCli?: pg.PoolClient) => Promise<WAMessageDto[]>;
  upset(data: WAMessageDto): (dbTrx?: pg.PoolClient) => Promise<void>;
  read(account_id: string, id: string): (dbCli?: pg.PoolClient) => Promise<WAMessageDto | undefined>;
}

export namespace IWAMessagesDAO {

  export type WAMessagesFilter = {
    conversation_avatar_id?: number,
    conversation_contact_id?: number,
    conversation_contact_phonenumber?:string,
    /** Algun tag de la conversación está incluido aquí */
    conversation_tags_id_someof?: number[]
    /** Ningún tag de la conversación está incluido aquí */
    conversation_tags_id_noneof?: number[]      
          
    account_id?: string,
    /** Cuentas en activo pueden estar habilitadas/deshabilitadas */
    account_enabled?: boolean,
    /** Cuenta retirada (no habilitable) */
    account_historic?: boolean,
    id?: string,
    ids?: string[],
    from_id?: string,
    to_id?: string,
    /** Contacto que soy yo (el contacto de la cuenta de whatsapp). Su id equivale al identificador de la cuenta (account_id) */
    me_id?:string
    /** Contacto que no soy yo (el otro) */
    other_id?: string,
    /** stamp único (por avatar) mayor que */
    uniquestamp_gt?:Date,
    /** stamp mayor o igual que */
    stamp_gte?: Date,
    /** stamp único (por avatar) mayor o igual que */
    uniquestamp_gte?:Date,
    /** stamp menor estricto de */
    stamp_lt?: Date,
    /** stamp único (por avatar) menor extricto que */
    uniquestamp_lt?:Date,
    /** stamp menor o igual que */
    stamp_lte?: Date,
    /** stamp único (por avatar) menor o igual que */
    uniquestamp_lte?:Date,
    /** Solo el últo mensaje de cada conversación (Conversación entre un Contacto y un Avatar*/
    onlylast?: boolean,
    /** Dirección de los mensajes */
    direction?: MessageDirection,
    /** Incluir datos del contacto al que se ha enviado o que nos ha enviado el mensaje, (El contacto que no es el de la cuenta) */
    include_other?:boolean,
    /** Incluir datos del contacto que representa a la cuenta whatsapp */
    include_me?: boolean,
    /** Incluir datos de la conversación (conversation->avatar y conversation->contact) */
    include_conversation?:boolean,
    /** Excluir los datos originales del mensaje aportados por whatsappweb.js */
    include_original?:boolean,
    /** Orden del resultado.  stamp_asc y stamp_desc usan, internamente, uniquestamp para asegurar un orden predecible (por avatar) */
    orderby?: "stamp_asc" | "stamp_desc" | "id_asc" | "id_desc",
    page_offset?: number,
    page_limit?: number,
  }
}

