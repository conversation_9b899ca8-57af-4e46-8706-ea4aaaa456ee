import { throwUndefinedError, throwUnexpectedError, throwUnknownEntityError } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { WhatsappClients } from "lib/external/impl/WhatsappClients";
import { IWhatsappClients } from "lib/external/IWhatsappClients";
import * as QRCode from "qrcode";
import { promisify } from "util";
import { WAAccountDto } from "whatsapp-srv-dtos";
import WAWebJS from "whatsapp-web.js";
import { IWAClientsBusiness } from "../IWAClientsBusiness";
import { WAWebJS2Dto } from "./common/WAWebJS2Dto";
import { SyncWa2Db } from "./SyncWa2Db";


/**
 * Gestiona el envío y recepción de mensajes a través de *waclients* y del repositorio de BBDD
 * 
 * @param param0 
 * @returns 
 */
export function WAClientsBusiness({ hooksSrv, db: { withTransaction }, waclients, logger, loggerUtils, wamessagesDAO, waaccountsDAO, smtp, deps }: IContainer): IWAClientsBusiness {
  // Private providers
  const syncWa2Db = SyncWa2Db(deps);

  let reviewInterval: NodeJS.Timer | undefined;
  // Clientes que actualmente tienen un proceso en ejecución
  let reviewSemaphore = new Set<string>();

  return {
    getClientStatus,
    getClientSnapshotAsJpeg,
    bootstrap,
    send,
    dispose
  }

  async function send(client_id: string, to: string, text: string): Promise<IWAClientsBusiness.sendResult> {
    try {
      // Verificamos que el cliente existe en la BBDD
      const client = await waaccountsDAO.read({ id: client_id })();
      if (!client)
        return { code: IWAClientsBusiness.sendErrorCode.err_unknown_client };
      else if (!client.enabled)
        return { code: IWAClientsBusiness.sendErrorCode.err_client_disabled };
      // Importar el contact de Whatasapp si aún no existe en la BBDD
      const contact = await syncWa2Db.syncContactByNumberIfNotExists(client_id, to);
      if (!contact)
        // Toda la comunicción ha ido bien, pero no hay un contacto
        return { code: IWAClientsBusiness.sendErrorCode.err_unknown_contact };
      // Enviamos el mensaje
      const wawMsg = await waclients.sendMessage(client_id, to, text);
      // Registro en la BBDD
      const dbMessageDto = await withTransaction(async (trx) => {
        const msgDto = WAWebJS2Dto.toWAMessageDto(client_id, wawMsg);
        // Update/Insert del mensaje en la BBDD
        await wamessagesDAO.upset(msgDto)(trx);
        // Obtenemos la versión actual registrada en la BBDD (incluye información generada durante el insert)
        return await wamessagesDAO.read(client_id, msgDto.id!)(trx) ?? throwUnexpectedError("updated/inserted message doesn't exist!!!");
      })();
      return { code: IWAClientsBusiness.sendResultCode.ok, sentMessage: dbMessageDto };

    } catch (e) {
      const code =
        (e instanceof IWhatsappClients.UnknownContactError) ? IWAClientsBusiness.sendErrorCode.err_unknown_contact :
          (e instanceof IWhatsappClients.UnknownPhonenumberError) ? IWAClientsBusiness.sendErrorCode.err_unknown_contact :
            (e instanceof IWhatsappClients.NoContactChatError) ? IWAClientsBusiness.sendErrorCode.err_no_chat :
              (e instanceof IWhatsappClients.UnitializedClientError) ? IWAClientsBusiness.sendErrorCode.err_other :
                // Que waclients no conozca un cliente no significa que no lo tengamos en la BBDD, 
                // solo significa que de momento no se ha iniciado su "bootstrap" o ha tenido que ser descargado temporalmente
                (e instanceof IWhatsappClients.UnknownClientError) ? IWAClientsBusiness.sendErrorCode.err_other :
                  IWAClientsBusiness.sendErrorCode.err_other;

      if (code === IWAClientsBusiness.sendErrorCode.err_other && e instanceof Error)
        logger.error(`Error inesperado enviando mensaje ${e.name} ${e.message}`);

      return { code };
    }
  }

  function getClientStatus(client_id: string) {
    return waclients.getClientStatus(client_id);
  }

  async function getClientSnapshotAsJpeg(client_id: string) {
    if (waclients.getClientStatus(client_id) !== WhatsappClients.clientStatus.unknown)
      return waclients.getClientSnapshotAsJpeg(client_id);
    else
      return void 0;
  }


  async function bootstrap() {
    // Cualquier mensaje recibido es regisrado en la BBDD
    waclients.emmiter.
      on("message_received", onMessageReceived).
      on("bind", onBind).
      on("binded", onBinded).
      on("bad_binding", onBadBinding);


    // Arrancamos los clientes whatsapp.
    await reviewAllAccountsClients();

    // Cada medio minuto revisa si los clientes están activos
    reviewInterval = setInterval(() =>
      reviewAllAccountsClients().catch(e => { logger.error("Problems reviewing clients", e) }),
      1.5 * 60 * 1000
    );
  }

  async function dispose() {
    if (reviewInterval)
      clearInterval(reviewInterval);

    waclients.emmiter.
      off("message_received", onMessageReceived).
      off("bind", onBind).
      off("binded", onBinded).
      off("bad_binding", onBadBinding);
  }

  //#region event handlers
  async function onMessageReceived({ clientNumber, message }: { clientNumber: string, message: WAWebJS.Message }) {
    await loggerUtils.withDebug(`${clientNumber}: message_received ${message.from}`)(async () => {
      // Quizás no tengamos en contacto en la BBDD
      if (message.broadcast || message.from === "status@broadcast") {
        logger.debug(`Bloadcast message ${JSON.stringify(message.rawData)}`);
      } else {
        const messageDTO = WAWebJS2Dto.toWAMessageDto(clientNumber, message);
        const contactNumber = messageDTO.from!.id! ?? throwUnexpectedError("Can't determine message from contact number"); // Convención: Usamos el número de teléfono como ID
        await syncWa2Db.syncContactByNumberIfNotExists(clientNumber, contactNumber) ?? throwUnknownEntityError(`Cant determine client's ${clientNumber} contact ${message.from}  `);
        // Incorporamos el mensaje a la BBDD
        await wamessagesDAO.upset(messageDTO)();
        // Leemos para informar a los hooks

        const rdMessage = await wamessagesDAO.read(clientNumber, messageDTO.id!)();
        if (rdMessage) hooksSrv.onMessageReceived(rdMessage);
      }
    }).catch(_ => void 0);
  }
  async function onBind({ clientNumber, qr }: { clientNumber: string, qr: string }) {
    await notify_BindQR(clientNumber, qr);
  }
  async function onBadBinding({ clientNumber, bindedNumber }: { clientNumber: string, bindedNumber: string }) {
    await notify_BadBinding(clientNumber, bindedNumber);
  }
  async function onBinded({ clientNumber }: { clientNumber: string }) {
    const snapshot = await waclients.getClientSnapshotAsJpeg(clientNumber).catch(_ => void 0);
    await notify_Binded(clientNumber, snapshot);
  }
  //#endregion

  /**
   * Revisar si están en ejecución todos los clientes.
   * - Si alguno no lo está, se iniciará y sincronizarán sus contactos/chats.
   * - NO esperamos a que el cliente haya acabado de iniciarse y sus contactos se hayan sincronizado:  quedarán como una corrutina "autónoma" que acabará tarde o temprano.
   */
  async function reviewAllAccountsClients() {
    const accounts = await waaccountsDAO.list()();
    for (const account of accounts) {
      const lockKey = account.id!;
      if (!reviewSemaphore.has(lockKey)) {
        reviewSemaphore.add(lockKey);
        // Lanzamos "corrutina" sin esperar su finalización. 
        reviewAccountClient(account)
          .catch(_ =>
            logger.error(`Can't activate client for account ${account}`))
          .finally(() =>
            reviewSemaphore.delete(lockKey)
          );
      }
    }
  }


  /**
   * Si el cliente asociado a la cuenta no está arrancado, se arranca y se sincronizan sus contactos y chats.
   * @param account 
   */
  async function reviewAccountClient(account: WAAccountDto) {
    if (!account.id) {
      throwUndefinedError("account.id");
    }


    if (account.enabled) {
      if (waclients.getClientStatus(account.id) === WhatsappClients.clientStatus.unknown) {
        await waclients.bootstrapClient(account.id);
        // Siempre que conseguimos conectar un cliente, refrescamos sus contactos y sus chats.
        try { await syncWa2Db.syncContacts(account.id); } catch (e) { logger.error(e); }
        try { await syncWa2Db.syncChats(account.id); } catch (e) { logger.error(e); }
      } /*else if(Math.random()>0.999) {
        // Refresco random de contactos y chats... 1 de cada 1000 veces
        logger.info("Sincronización aleatoria de contactos y chats")
        try { await syncWa2Db.syncContacts(account.id); } catch (e) { logger.error(e); }
        try { await syncWa2Db.syncChats(account.id); } catch (e) { logger.error(e); }
      } */
    } else {
      if (waclients.getClientStatus(account.id) !== WhatsappClients.clientStatus.unknown) {
        await waclients.shutdownClient(account.id)
      }
    }

  }


  //#region notificaciones por email
  function notify_BindQR(clientNumber: string, qr: string) {
    //Eliminamos los mails de vinculacion 
    return true;
    return loggerUtils.withDebug(`notify_BindQR ${clientNumber}`)(() => withAccount(clientNumber)(async accountDto =>
      smtp.sendWithNoWait({
        to: accountDto.notificationemail,
        subject: `Vincular web.whatsapp ${clientNumber}`,
        html:
          `<body>` +
          `  <h1>Escanea este qr con teléfono ${clientNumber}</h1>` +
          `  <p><img src="${await promisify(QRCode.toDataURL)(qr)}" /></p>` +
          `</body>`
      })
    )).catch(_ => void 0);
  }

  function notify_Binded(clientNumber: string, jpg: Buffer | undefined) {

    return loggerUtils.withDebug(`notify_Binded ${clientNumber}`)(() =>
      withAccount(clientNumber)(async accountDto =>
        smtp.sendWithNoWait({
          to: accountDto.notificationemail,
          subject: `${clientNumber} vinculado`,
          html: [
            `<!DOCTYPE html>`,
            `<html>`,
            `<body>`,
            `  <h3>Has vinculado correctamente ${clientNumber}</h3>`,
            `  <h4>Aquí tienes una captura del cliente web:</h4>`,
            (jpg !== void 0) ? `<p><img src="data:image/jpeg;base64,${jpg!.toString("base64")}" /></p>` : "",
            `</body>`,
            "</html>"
          ].join("\n")
        })
      )
    ).catch(_ => void 0);

  }


  function notify_BadBinding(clientNumber: string, bindedNumber: string) {
    return loggerUtils.withDebug(`notify_BadBinding ${clientNumber}`)(() =>
      withAccount(clientNumber)(async accountDto =>
        smtp.sendWithNoWait({
          to: accountDto.notificationemail,
          subject: `Has vinculado dispositivo equivocado a ${clientNumber}`,
          html:
            `<body>` +
            `<h3>Has vinculado dispositivo equivocado a ${clientNumber}</h3>` +
            `<p>` +
            `  Se pidió vincular a teléfono ${clientNumber} y lo has vinculado usando el teléfono ${bindedNumber}.<br/>` +
            `  El vínculo ha sido cerrado después de establecerse. ` +
            `</p>` +
            `<p style="color:red;">` +
            `  ¡¡¡Ten más cuidado cuando se te vuelva a pedir escanear el QR !!!` +
            `</p>` +
            `</body>`
        })
      )
    ).catch(_ => void 0)
  }

  function withAccount(id: string) {
    return async <T>(fAccount: (account: WAAccountDto) => T) =>
      fAccount(
        await waaccountsDAO.read({ id })() ?? throwUnknownEntityError(`Can't determine client ${id}`)
      )
  }
  // #endregion

}