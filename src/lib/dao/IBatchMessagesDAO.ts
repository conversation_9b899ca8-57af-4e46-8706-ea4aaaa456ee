import { BatchMessageDto, BatchTaskKeyStruct, BatchMessageKeyStruct, BatchMessageStatusCode, BatchMessageStruct }  from "whatsapp-srv-dtos";

import * as pg from "pg";


export type BatchMessagesFilter = {
  task_id?: string,
  id?: number,
  from_number?: string,
  to_number?: string,
  status_code?: BatchMessageStatusCode,
  sortby?: "id_asc" | "id_desc" | "task_id_asc_id_asc" | "task_id_desc_id_desc" | "triescount_asc_id_asc" |  "task_id_asc_triescount_asc_id_asc",
  pagination?: {
    limit?: number
    offset?: number
  }
}

export interface IBatchMessagesDAO {
  /**
   * Obtener una lista de mensajes batch
   * @param data 
   */
  list(data?: BatchMessagesFilter): (dbTrx?: pg.PoolClient) => Promise<BatchMessageStruct[]>;
  /**
   * Leer un mensaje batch
   * @param pk 
   */
  read(pk: BatchMessageKeyStruct): (dbCli?: pg.PoolClient) => Promise<BatchMessageStruct | undefined>
  /**
   * Crear un mensaje batch
   * @param data 
   */
  create(data: BatchMessageDto): (dbTrx?: pg.PoolClient) => Promise<BatchMessageKeyStruct>
  /**
   * Modificar un mensaje batch
   * @param data 
   */
  update(data: BatchMessageDto): (dbTrx?: pg.PoolClient) => Promise<number>
  /**
   * Incrementar el contador de intentos de envios del mensaje batch
   * @param pk 
   */
  inc_triescount(pk: BatchMessageKeyStruct): (dbTrx?: pg.PoolClient) => Promise<number>
  /**
   * Cancelar todos los mensajes batchs pendientes de una tarea.
   * Los mensajes afectados tendrán:
   *   status = 'error'
   *   error_code = 'deadline_exceeded'
   * @param taskPK 
   */
  set_pending_to_deadline_exceeded_by_task(taskPK: BatchTaskKeyStruct): (dbTrx?: pg.PoolClient) => Promise<number>
}

