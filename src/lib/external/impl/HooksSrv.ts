import { jsonArrayMember, jsonMember, jsonObject } from "typedjson"
import { IHooksSrv } from "../IHooksSrv"
import { IContainer } from "container/IContainer"
import { WAMessageDto } from "whatsapp-srv-dtos";

import axios,{ AxiosRequestConfig } from "axios";
//import { RequestInit } from "node-fetch";
//import fetch from "node-fetch";


  /**
   * Notas de implementación:
   * - Cuando se usa un hook, éste no se envía de forma inmediata, sino que se encola para ello.  
   * - Se hace uso de un setInterval para ejecutar el envío real de mensajes a los hoojs
   */
export function HooksSrv({ logger, hooksOptions }: IContainer): IHooksSrv {

  type TQueueRequest = {
    url: string,
    options: AxiosRequestConfig
  }
  const requestsQueue: TQueueRequest[] = [];

  let disposed = false;
  let executing = false;
  const timer = setInterval(async () => {
    if (!executing) {
      executing = true;
      while (!disposed && requestsQueue.length !== 0) {
        const { url, options } = requestsQueue.splice(0, 1)[0]
        try {
          logger.info(`executing hook ${options.method} ${url}`);
          const result = await axios({
            url:url,
            ...options
          });
          logger.info(`status: ${result.status}`);
        } catch (e) {
          logger.error(`Problemns executing hook ${options.method} ${url}`, e);
        }
      }
      executing = false;
    }
  }, 5000);

  return {
    onMessageReceived,
    dispose
  }

  function onMessageReceived(msg: WAMessageDto) {
    if (hooksOptions.onMessageReceived) {
      for (const requestOptions of hooksOptions.onMessageReceived) {
        requestsQueue.push({
          url: requestOptions.url,
          options: {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            data: msg,
          }
        });
      }
    }
  }
  async function dispose() {
    if (!disposed) {
      clearInterval(timer);
      disposed = true;
    }
  }



}

export namespace HooksSrv {

  @jsonObject()
  export class RequestOptions {
    @jsonMember(String, { isRequired: true })
    method!: string
    @jsonMember(String, { isRequired: true })
    url!: string
  }

  @jsonObject()
  export class Options {
    @jsonArrayMember(RequestOptions, { isRequired: false })
    onMessageReceived?: RequestOptions[]
  }


}