import { Serializable, TypedJ<PERSON><PERSON> } from "typedjson";
import { ErrUtils } from 'agentor-lib'; const { doThrowError } = ErrUtils;

export namespace TypedJsonUtils {

  export const parse = <T>(object: any, rootType: Serializable<T>) =>
    TypedJSON.parse<T>(object, rootType, { errorHandler: (e: Error) => { throw e; }, }) as T;

  export function oneOfDeserializer<T>(acceptedValues: T[]) {
    const acceptedSet = new Set<T>(acceptedValues);
    return (json: any) =>
      json === null || json === void 0 ? json :
        acceptedSet.has(json) ? json :
          doThrowError(`Value ${json} is not into acceptable ones`);
  }

  /**
   * Deserializa un elemento "string" a int  ("number" sin decimales)
   * Si el elemento ya es number, devuelve su parte entera
   * @returns 
   */
  export function strAsIntDeserializer() {
    return (json: any) => {
      if (json === null || json === void 0)
        return json;
      else if (typeof (json) === "number")
        return Math.floor(json as number);
      else if (typeof (json) === "string") {
        let value = parseInt(json as string);
        if (isNaN(value))
          throw new TypeError("Value is not a finite numeric string");
        else
          return value;
      }
      else
        throw new TypeError(`Number expected, found ${typeof (json)}`);

    }

  }

  export function strAsArrayIntDeserializer() {
    return (json: any):number[]|null|undefined => {
      if (json === null || json === void 0)
        return json;
      else if (typeof (json) === "number")
        return [Math.floor(json as number)];
      else if (typeof (json) === "string") {
        let values = (json as string).split(",").map( sInt=>parseInt(sInt as string) );
        if (values.some( v=> isNaN(v)))
          throw new TypeError("Value must be a comma separated finite numeric values");
        else
          return values;
      }
      else
        throw new TypeError(`Number[] expected, found ${typeof (json)}`);

    }

  }

  /**
   * Deserializa un elemento "string" a int  ("number" sin decimales)
   * Si el elemento ya es number, devuelve su parte entera
   * @returns 
   */
  export function strAsBoolDeserializer() {
    return (json: any) => {
      if (json === null || json === void 0)
        return json;
      else if (typeof (json) === "boolean")
        return json;
      else if (typeof (json) === "string") {
        switch (json.trim()) {
          case "1":
          case "true":
            return true;
          case "0":
          case "false":
            return false;
          default:
            throw new TypeError("Value must be '0'|'false'|'1'|'true'");
        }
      }
      else
        throw new TypeError(`Boolean expected, found ${typeof (json)}`);

    }

  }
}