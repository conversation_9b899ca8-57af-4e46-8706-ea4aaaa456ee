DO $$
DECLARE
  new_version integer:=13;
BEGIN

  IF (select max(version) from public.schema_changes)=(new_version-1) THEN
    /* BEGIN */

    -- RETIRAR CUENTAS DEL AVATAR DE ENTRENAMIENTO (**********)
    -- ELIMINAMOS: Envios, mensajes, contactos y finalmente cuentas.
    
    --batchsends: Borramos todos los envios
    delete from batchsends where sentmessage_account_id = '***********' or sentmessage_account_id ='***********';
        
    -- wamessages: Borramos todos los mensajes
    delete from wamessages where account_id ='***********' or account_id ='***********';

    -- wacontacts: Borramos todos los cantactos de las lineas
    delete from wacontacts where (account_id ='***********' or account_id ='***********');

    -- waaccounts: Quitamos las cuentas del avatar de entrenamiento
    delete from waaccounts where (id ='***********' or id ='***********');

    -- conversations: Dejamos el avatar "en blanco"
   delete from conversations where avatar_id =**********;


    /* END */
    INSERT INTO public.schema_changes (version, script) VALUES (new_version, to_char(new_version,'FM000')||'.sql');
  END IF;

END;
$$;