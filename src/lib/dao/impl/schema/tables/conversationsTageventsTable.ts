import { DBSchema } from "agentor-lib";

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum conversationsTageventsFields {
  id = "id",
  conversation_avatar_id = "conversation_avatar_id",
  conversation_contact_id = "conversation_contact_id",
  tag_id = "tag_id",
  at = "at",
  op_code = "op_code", //character varying NOT NULL CHECK (op_code = 'c' OR op_code = 'd' ),
}

const fldN = conversationsTageventsFields;

export const conversationsTageventsTable = new DBSchema.TableSchema(
  "conversations_tagevents",
  [
    new DBSchema.TableFieldSchema(fldN.id, { c, r, nn, pk }),
    new DBSchema.TableFieldSchema(fldN.conversation_avatar_id, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.conversation_contact_id, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.tag_id, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.at, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.op_code, { c, r, nn })
  ],
  [
    new DBSchema.TableFKSchema("conversation"),
    new DBSchema.TableFKSchema("tag"),
  ]
);