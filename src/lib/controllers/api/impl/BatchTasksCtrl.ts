import { IContainer } from "container/IContainer";
import { Request } from "express";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { jsonArrayMember, jsonMember, jsonObject } from "typedjson";
import { BatchMessageDto, BatchMessageStatusCode, BatchTaskDto, MessageContentType } from "whatsapp-srv-dtos";
import { IBatchTasksCtrl } from "../IBatchTasksCtrl";

export function BatchTasksCtrl({
  logger,
  db: { withTransaction },
  restUtils: { asRest, asValidRequest },
  controllerUtils: { withAuthorizedUser },
  batchmessagesDAO,
  batchtasksDAO,
}: IContainer): IBatchTasksCtrl {

  return {
    postTask: asRest({ nullAs404: true }, asValidRequest("/batchtasks/post_task", postTask)),
  }

  // #region API
  async function postTask(req: Request) {
    return withAuthorizedUser("", async (user) => {
      const postBody = TypedJsonUtils.parse(req.body, BatchTasksStructs.BatchTaskPostBody);
      return await withTransaction(
        async trx => {
          const taskPk = await batchtasksDAO.create(postBody)(trx);
          for (let nMessage = 0; nMessage < postBody.messages.length; nMessage++) {
            await batchmessagesDAO.create({
              ...postBody.messages[nMessage],
              task: taskPk,
              id: nMessage,
              status: {
                code: BatchMessageStatusCode.pending
              },
            })(trx);
          }
          return batchtasksDAO.read(taskPk)(trx);
        }
      )()

    })(req);

  }
  // #endregion


}
/**
 * Estructuras de datos que podemos encontrar en Body, Params, Query...
 */
namespace BatchTasksStructs {
  @jsonObject()
  class BatchTaskScheduling {
    @jsonMember(Date, { isRequired: true })
    start!: Date
    @jsonMember(Date, { isRequired: true })
    deadline!: Date
  }
  @jsonObject()
  class BatchMessageContact {
    @jsonMember(String, { isRequired: true })
    number!: string
  }
  @jsonObject()
  class BatchMessageContent {
    @jsonMember(String, { isRequired: true })
    body!: string
    @jsonMember(String, { isRequired: true, deserializer: ((v: string) => MessageContentType.fromString(v)) })
    type!: MessageContentType
  }
  @jsonObject()
  class BatchMessage implements BatchMessageDto {
    @jsonMember(BatchMessageContact, { isRequired: true })
    from!: BatchMessageContact
    @jsonMember(BatchMessageContact, { isRequired: true })
    to!: BatchMessageContact
    @jsonMember(BatchMessageContent, { isRequired: true })
    content!: BatchMessageContent
  }
  @jsonObject()
  export class BatchTaskPostBody implements BatchTaskDto {
    @jsonMember(BatchTaskScheduling, { isRequired: true })
    scheduling!: BatchTaskScheduling;
    @jsonArrayMember(BatchMessage, { isRequired: true })
    messages!: BatchMessage[]
  }
}