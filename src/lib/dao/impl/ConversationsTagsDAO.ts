import { Db<PERSON>rudUtils, SqlHelpers, SqlWhereHelpers } from "agentor-lib";
import { IContainer_db } from "container/IContainer";
import { ConversationtagDto, ConversationtageventDto, ConversationtageventStructDto, ConversationtagPkDto, ConversationtagStructDto, ConvTagOpCode } from "whatsapp-srv-dtos";
import { ConversationstagsFilter, IConversationstagsDAO } from "../IConversationsTagsDAO";
import { row2DTO } from "./row2dto";
import { AvatarsFields } from "./schema/tables/avatarsTable";
import { conversationsTageventsTable } from "./schema/tables/conversationsTageventsTable";
import { conversationsTagsFields, conversationsTagsTable } from "./schema/tables/conversationsTagsTable";

const
  { insertRecord, updateRecord, removeRecord } = DbCrudUtils,
  { buildSqlFields, buildSqlFrom } = SqlHelpers,
  { addEq } = SqlWhereHelpers
  ;

export function ConversationtagsDAO({ db, dbSchema }: IContainer_db): IConversationstagsDAO {
  const flds = conversationsTagsFields;
  const alias = "cvtg";

  return {
    list,
    count,
    create,
    read,
    update,
    remove
    //readByApikey,
  };
  function list(filter: ConversationstagsFilter = {}) {
    const { values, terms, fromPaths, fieldsPaths } = buildConditions(filter);
    const
      qryOptions = {
        table: conversationsTagsTable,
        tableAlias: alias,
      };
    const
      qryFields = buildSqlFields(dbSchema, { ...qryOptions, allowedPaths: fieldsPaths }).join(', '),
      qryFrom = buildSqlFrom(dbSchema, { ...qryOptions, allowedPaths: fromPaths });
    const
      qry = `select ${qryFields} from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli => {
      const rows = await dbCli.query(qry, values).then(result => result.rows);
      return rows.map(row => row2DTO.toConversationTag(row, alias))
    });
  }
  function count(filter: ConversationstagsFilter = {}) {
    const { values, terms, fromPaths } = buildConditions(filter);
    const
      qryFromOptions = {
        table: conversationsTagsTable,
        tableAlias: alias,
        allowedPaths: fromPaths
      };
    const
      qryFrom = buildSqlFrom(dbSchema, qryFromOptions);

    const qry = `select count(*) as "howMany" from ${qryFrom} where ${terms.join(" and ")}`;

    return db.withClient(async dbCli =>
      dbCli.query(qry, values).then(
        result => parseInt(result.rows[0]["howMany"])
      )
    );
  }

  function read(key: ConversationtagPkDto) {
    return db.withClient(async cli => {
      const [conversationTag] = await list({
        conversation_avatar_id: key.conversation.avatar.id,
        conversation_contact_id: key.conversation.contact.id,
        tag_id: key.tag.id
      })(cli);
      return conversationTag;
    });
  }
  function update(key: ConversationtagPkDto, data: ConversationtagDto) {
    return db.withTransaction(updateRecord(conversationsTagsTable, { ...data, ...key }));
  }
  function create(data: ConversationtagStructDto) {
    return db.withTransaction(async trx => {
      const rw = await insertRecord(conversationsTagsTable, data)(trx);
      const result = row2DTO.toConversationTagPk(rw);
      {
        const event = { ...result, op: { code: "c" }, at: new Date() } as ConversationtageventStructDto;
        await insertRecord(conversationsTageventsTable, event)(trx);
      }
      return result;
    });
  }
  function remove(key: ConversationtagPkDto,) {
    return db.withTransaction(async (trx) => {
      const n = await removeRecord(conversationsTagsTable, key)(trx);
      if (n !== 0) {
        const event = { ...key, op: { code: "d" }, at: new Date() } as ConversationtageventStructDto;
        await insertRecord(conversationsTageventsTable, event)(trx);
      }
      return n;
    });
  }

  function buildConditions(filter: ConversationstagsFilter = {}) {
    var values: any[] = [];
    var terms: string[] = ["true"];
    var fromPaths: Set<string> = new Set([alias]); // Qué paths deben resolver las joins en el from
    var fieldsPaths: Set<string> = new Set([alias]);

    addEq(terms, values, `${alias}.${flds.conversation_avatar_id}`, filter.conversation_avatar_id);
    addEq(terms, values, `${alias}.${flds.conversation_contact_id}`, filter.conversation_contact_id);
    addEq(terms, values, `${alias}.${flds.tag_id}`, filter.tag_id);
    if (filter.domain_id !== void 0) {
      // La condición sobre el dominio la hacemos sobre el avatar. 
      // Podría ser sobre el tag o el contacto (que son del mismo dominio), pero el número de avatares 
      // se prevee menor que de contactos y, posíblemente, tags (y la join será más eficiente)
      addEq(terms, values, `${alias}_conversation_avatar.${AvatarsFields.domain_id}`, filter.domain_id);
      fromPaths.add(`${alias}_conversation_avatar`);
    }
    if (filter.detail_conversation_avatar) {
      fromPaths.add(`${alias}_conversation_avatar`);
      fieldsPaths.add(`${alias}_conversation_avatar`);
    }
    if (filter.detail_conversation_contact) {
      fromPaths.add(`${alias}_conversation_contact`);
      fieldsPaths.add(`${alias}_conversation_contact`);
    }
    if (filter.detail_tag) {
      fromPaths.add(`${alias}_tag`);
      fieldsPaths.add(`${alias}_tag`);
    }

    return {
      values,
      terms,
      // Paths que deben ser resueltos por el From
      fromPaths: [...fromPaths.values()],
      // Paths que deben ser resueltos por el Select
      fieldsPaths: [...fieldsPaths.values()]
    };
  }
}

