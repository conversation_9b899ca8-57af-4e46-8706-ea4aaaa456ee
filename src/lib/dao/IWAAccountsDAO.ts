import { WAAccountDto, WAAccountPkDto }  from "whatsapp-srv-dtos";
import * as pg from "pg";

export type WAAccountsFilter = {
  id?: string,
  enabled?: boolean,
  historic?: boolean,
  avatar_id?: number,
  avatar_domain_id?: number, 
  //apikey?: string,
}
export interface IWAAccountsDAO {
  list(filter?: WAAccountsFilter): (dbCli?: pg.PoolClient) => Promise<WAAccountDto[]>;
  create(data: WAAccountDto): (dbTrx?: pg.PoolClient) => Promise<WAAccountPkDto>;
  update(pk:WAAccountDto, data: WAAccountDto): (dbTrx?: pg.PoolClient) => Promise<number|undefined>;
  read(pk: WAAccountPkDto): (dbCli?: pg.PoolClient) => Promise<WAAccountDto | undefined>;
  //readByApikey(apikey: string): (dbCli?: pg.PoolClient) => Promise<WAAccountDTO | undefined>;
}