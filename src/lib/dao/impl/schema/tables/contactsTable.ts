import { DBSchema } from "agentor-lib";

const [c, r, u, pk, nn] = [true, true, true, true, true];

export enum contactsFields {
  id = "id",
  domain_id="domain_id",
  name="name",
  phonenumber="phonenumber"
}


const fldN = contactsFields;

export const contactsTable = new DBSchema.TableSchema(
  "contacts",
  [    
    new DBSchema.TableFieldSchema(fldN.id, { c, r, nn, pk }),
    new DBSchema.TableFieldSchema(fldN.domain_id, { c, r, nn }),
    new DBSchema.TableFieldSchema(fldN.name, { c, r, u, nn }),
    new DBSchema.TableFieldSchema(fldN.phonenumber, { c, r, nn }),
  ],
  [
    new DBSchema.TableFKSchema("domain"),
  ]
);