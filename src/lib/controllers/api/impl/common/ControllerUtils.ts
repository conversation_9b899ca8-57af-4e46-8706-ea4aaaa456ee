import { Request } from "express";
import { decodeAccessToken, JsonWebTokenError, NotBeforeError, RestError, TokenExpiredError } from "agentor-lib";
import { IContainer } from "container/IContainer";
import { AccessTokenPayload, IControllerUtils } from "../../IControllerUtils";
import { TypedJsonUtils } from "lib/TypedJsonUtils";
import { UserDto, UserStruct } from "whatsapp-srv-dtos";

const C_TOPBROKERS_API_KEY = "Topbrokers-Api-Key".toLowerCase();
const C_AUTHORIZATION = "Authorization".toLowerCase();
// Access token en la URL
const C_ACCESS_TOKEN_PARAM = "access_token";

export function ControllerUtils({ apikeysDAO, usersDAO }: IContainer): IControllerUtils {

  return {
    withAuthorizedUser
  };

  /**
   * Valida si el usuario que efectúa el request está autorizado para realizar las operaciones indicadas en "scpe" .
   * A destacar:
   *   - Si se indica la cabecera Topbrokers-Api-Key, se usa la ApiKey para deducir los datos del usuario.
   *   - En caso contrario, se espera un Authorization Bearer con un token JWT.
   * Una vez validado, se evoca la función fUser usando como parámetro el objeto con los datos del usuario.
   * 
   * @param scope No se hace caso de este dato (actualmente)
   * @param fUser Función que se evocará con el objeto usuario
   */
  function withAuthorizedUser<T>(scope: string, fUser: (user: UserStruct) => Promise<T>) {
    return async function (req: Request) {

      const apiKeyHeader = getHeader(req, C_TOPBROKERS_API_KEY);
      const authorizationHeader = getHeader(req, C_AUTHORIZATION);
      const accessTokenQueryparam = req.query[C_ACCESS_TOKEN_PARAM]?.toString()

      if (apiKeyHeader) {
        let apikeyDto = await apikeysDAO.read(apiKeyHeader)();
        let user = apikeyDto?.user?.id ?
          await usersDAO.read(apikeyDto.user.id)() :
          void 0;
        if (user === void 0)
          throw new RestError({ status: 403, message: "Unknown Api key" });
        else
          return fUser(user);
      } else if (authorizationHeader) {
        let userId = await autorizationToUserId(scope, authorizationHeader);
        let user = await usersDAO.read(userId)();
        if (user === void 0)
          throw new RestError({ status: 403, message: "Not valid authorization header" });
        else
          return fUser(user);
      } else if (accessTokenQueryparam) {
        let userId = await accesstokenToUserId(scope, accessTokenQueryparam);
        let user = await usersDAO.read(userId)();
        if (user === void 0)
          throw new RestError({ status: 403, message: "Not valid access token query param" });
        else
          return fUser(user);
      } else {
        throw new RestError({ status: 400, message: `"No ${C_AUTHORIZATION} header or ${C_ACCESS_TOKEN_PARAM} query param found` })
      }
    }
  }

  /**
   * Esta función substituye al validador externo que a partir del token de autorización y el scope con los derechos necesarios 
   * Realiza las validaciones necesarias.
   * @param scope 
   * @param authorizationValue 
   * @returns 
   */
  async function autorizationToUserId(scope: string, authorizationValue: string) {
    const [bearer, accessToken] = authorizationValue.split(' ');
    if (bearer?.toLowerCase() !== 'bearer')
      throw new RestError({ status: 400, message: "Expected Authorization: Bearer ..." });
    else
      return accesstokenToUserId(scope, accessToken);
  }

  async function accesstokenToUserId(scope: string, accessToken: string) {
    let payload: any;
    try {
      payload = decodeAccessToken(accessToken);
    } catch (error) {
      if (error instanceof NotBeforeError || error instanceof TokenExpiredError)
        throw new RestError({ status: 403, message: error.message });
      else if (error instanceof JsonWebTokenError)
        throw new RestError({ status: 400, message: error.message });
      else
        throw error;
    }

    return TypedJsonUtils.parse(payload, AccessTokenPayload).user_id
  }

  function getHeader(req: Request, name: string): string | undefined {
    const headerValue = req.headers[name];

    return Array.isArray(headerValue) ? headerValue[0] : headerValue;
  }


}